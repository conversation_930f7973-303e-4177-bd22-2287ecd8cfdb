<template>
  <div class="chart-item">
    <SubTitle title="实时人口统计" />
    <div class="chart-container">
      <div id="populationChart" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'PersonnelRanking',
  components: {
    SubTitle,
  },
  data() {
    return {
      chartData: [],
      loading: false,
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    // 获取实时人口统计数据
    async fetchData() {
      try {
        this.loading = true
        // 使用与FertilityStatus相同的接口调用方式
        const titleType = '实时人口统计'
        const response = await getCsdnInterface1('rkhl_test', { type: titleType })

        console.log('实时人口统计接口返回数据:', response.data)

        // 处理接口返回的数据
        if (response.data && response.data.successful && response.data.data) {
          this.chartData = this.processChartData(response.data.data)
        } else {
          // 如果接口返回空数据或失败，使用默认数据
          this.chartData = [
            { name: '区域A', value: 1200 },
            { name: '区域B', value: 1000 },
            { name: '区域C', value: 800 },
            { name: '区域D', value: 600 },
          ]
        }
      } catch (error) {
        console.error('获取实时人口统计数据失败:', error)
        // 请求失败时使用默认数据
        this.chartData = [
          { name: '区域A', value: 1200 },
          { name: '区域B', value: 1000 },
          { name: '区域C', value: 800 },
          { name: '区域D', value: 600 },
        ]
      } finally {
        this.loading = false
        this.initChart()
      }
    },

    // 处理图表数据
    processChartData(data) {
      if (Array.isArray(data) && data.length > 0) {
        return data.map((item, index) => ({
          name: item.name || item.areaName || item.region || `区域${index + 1}`,
          value: item.value || item.count || item.population || 0,
        }))
      }

      // 如果数据格式不符合预期，返回默认数据
      return [
        { name: '区域A', value: 1200 },
        { name: '区域B', value: 1000 },
        { name: '区域C', value: 800 },
        { name: '区域D', value: 600 },
      ]
    },

    // 初始化柱状图
    initChart() {
      const chart = this.$echarts.init(document.getElementById('populationChart'))

      const categories = this.chartData.map((item) => item.name)
      const values = this.chartData.map((item) => item.value)

      const option = {
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#13C2C2',
          textStyle: { color: '#fff', fontSize: 24 },
          formatter: '{b}: {c}',
        },
        grid: { left: '10%', right: '10%', top: '14%', bottom: '12%' },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: { color: '#fff', fontSize: 24 },
          axisLine: { lineStyle: { color: '#333' } },
        },
        yAxis: {
          type: 'value',
          axisLabel: { color: '#fff', fontSize: 24 },
          axisLine: { lineStyle: { color: '#333' } },
          splitLine: { lineStyle: { color: '#333' } },
        },
        series: [
          {
            type: 'bar',
            data: values,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#13C2C2'
                  },
                  {
                    offset: 1,
                    color: 'rgba(19,194,194,0)'
                  }
                ]
              },
              borderRadius: [4, 4, 4, 4],
            },
            barWidth: 20,
            animationDuration: 1000,
            animationEasing: 'cubicOut',
          },
        ],
      }
      chart.setOption(option)

      // 响应式处理
      window.addEventListener('resize', () => {
        chart.resize()
      })
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>
