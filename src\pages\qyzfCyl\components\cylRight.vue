<!--
 * @Description: 产业链右侧组件
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-09-18 14:08:20
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-19 09:14:38
-->
<template>
  <div class="container">
    <div class="con">
      <div class="con-item">
        <MainTitle title="产业主体情况" />
        <div class="item-content cyzyClass">
          <div class="upClass">
            <div class="txtS">规上企业总数</div>
            <div class="countCard">
              <div class="number" v-for="(item, i) in '174'" :key="i">
                <div class="numbg" v-if="item != ',' && item != '.'">
                  <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                </div>
                <div v-else>{{ item }}</div>
              </div>
            </div>
            <div class="txtS">家</div>
          </div>

          <div class="midClass">
            <div class="cardS" @click="changeQy(0)">
              <div class="cardBg" v-show="qyIndex != 0"></div>
              <div class="cardBgTwo" v-show="qyIndex == 0"></div>
              <div class="txtTwo">链主培育企业</div>
              <div class="numberTwo">
                <div v-for="(item, i) in String(zsArr[1].zs)" :key="i">
                  <div class="numbgTwo" v-if="item != ',' && item != '.'">
                    <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                  </div>
                  <div class="numTwo" v-else>{{ item }}</div>
                </div>
                <span class="numtxt" style="font-size: 32px; margin-top: 38px">家</span>
              </div>
            </div>
            <div class="cardS" @click="changeQy(1)">
              <div class="cardBg" v-show="qyIndex != 1"></div>
              <div class="cardBgTwo" v-show="qyIndex == 1"></div>
              <div class="txtTwo">骨干企业</div>
              <div class="numberTwo">
                <div v-for="(item, i) in String(zsArr[2].zs)" :key="i">
                  <div class="numbgTwo" v-if="item != ',' && item != '.'">
                    <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                  </div>
                  <div class="numTwo" v-else>{{ item }}</div>
                </div>
                <span class="numtxt" style="font-size: 32px; margin-top: 38px">家</span>
              </div>
            </div>
            <div class="cardS" @click="changeQy(2)">
              <div class="cardBg" v-show="qyIndex != 2"></div>
              <div class="cardBgTwo" v-show="qyIndex == 2"></div>
              <div class="txtTwo">星火企业</div>
              <div class="numberTwo">
                <div v-for="(item, i) in String(zsArr[0].zs)" :key="i">
                  <div class="numbgTwo" v-if="item != ',' && item != '.'">
                    <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                  </div>
                  <div class="numTwo" v-else>{{ item }}</div>
                </div>
                <span class="numtxt" style="font-size: 32px; margin-top: 38px">家</span>
              </div>
            </div>
          </div>

          <div class="charOneClass" id="chartOne"></div>
        </div>
      </div>

      <div class="con-item">
        <MainTitle title="产业资源" />
        <div class="item-content cyzyClass">
          <div class="partOne">
            <SubTitle title="产业政策" />
            <div class="partBot">
              <div class="innerBox boderOne">
                <div class="ltxt">政策数量</div>
                <div class="numberTwo">
                  <div v-for="(item, i) in cyzc.zcsl" :key="i">
                    <div class="numtxt" v-if="item != ',' && item != '.' && item != '-'">
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                    </div>
                    <div class="numtxt" v-else>{{ item }}</div>
                  </div>
                  <span class="numtxt" style="font-size: 32px; margin-top: 64px">条</span>
                </div>
              </div>
              <div class="innerBox boderOne">
                <div class="ltxt">兑付企业数</div>
                <div class="numberTwo">
                  <div v-for="(item, i) in cyzc.dfqys" :key="i">
                    <div class="numtxt" v-if="item != ',' && item != '.' && item != '-'">
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                    </div>
                    <div class="numtxt" v-else>{{ item }}</div>
                  </div>
                  <span class="numtxt" style="font-size: 32px; margin-top: 64px">家</span>
                </div>
              </div>
              <div class="innerBox">
                <div class="ltxt">政策兑付金额</div>
                <div class="numberTwo">
                  <div v-for="(item, i) in cyzc.zcdfje" :key="i">
                    <div class="numtxt" v-if="item != ',' && item != '.' && item != '-'">
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                    </div>
                    <div class="numtxt" v-else>{{ item }}</div>
                  </div>
                  <span class="numtxt" style="font-size: 32px; margin-top: 64px">亿</span>
                </div>
              </div>
            </div>
          </div>

          <div class="partOne">
            <SubTitle title="人才资源" />
            <div class="partBot">
              <div class="innerBox boderOne">
                <div class="ltxt">乡贤人才</div>
                <div class="numberTwo">
                  <div v-for="(item, i) in rczy.xxrc" :key="i">
                    <div class="numtxt" v-if="item != ',' && item != '.' && item != '-'">
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                    </div>
                    <div class="numtxt" v-else>{{ item }}</div>
                  </div>
                  <span class="numtxt" style="font-size: 32px; margin-top: 64px">人</span>
                </div>
              </div>
              <div class="innerBox boderOne">
                <div class="ltxt">婺商企业家</div>
                <div class="numberTwo">
                  <!-- <div v-for="(item, i) in rczy.wsqyj" :key="i"> -->
                  <div v-for="(item, i) in '1345'" :key="i">
                    <div class="numtxt" v-if="item != ',' && item != '.' && item != '-'">
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                    </div>
                    <div class="numtxt" v-else>{{ item }}</div>
                  </div>
                  <span class="numtxt" style="font-size: 32px; margin-top: 64px">人</span>
                </div>
              </div>
              <div class="innerBox">
                <div class="ltxt">高素质劳动者</div>
                <div class="numberTwo">
                  <div v-for="(item, i) in rczy.gszldz" :key="i">
                    <div class="numtxt" v-if="item != ',' && item != '.' && item != '-'">
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                    </div>
                    <div class="numtxt" v-else>{{ item }}</div>
                  </div>
                  <span class="numtxt" style="font-size: 32px; margin-top: 64px">万</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="con">
      <div class="con-item">
        <MainTitle title="产业规模" />
        <div class="item-content cyzyClass">
          <div class="upTwoClass">
            <div class="upsClass">
              <div class="txtS">规上总产值</div>
              <div class="countCard">
                <div class="number" v-for="(item, i) in gszcz.value" :key="i">
                  <div class="numbg" v-if="item != ',' && item != '.'">
                    <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                  </div>
                  <div v-else>{{ item }}</div>
                </div>
              </div>
              <div class="txtS">亿元</div>
            </div>
            <div class="downClass">
              <div class="twoClass">
                <div class="tbLabel">增速</div>
                <div class="txtNum">{{ gszcz.zs }}%</div>
              </div>
            </div>
          </div>
          <div class="chartTwoClass" id="chartTwo"></div>
        </div>
      </div>

      <div class="con-item newbottom">
        <div class="partOne">
          <SubTitle title="技术攻关（揭榜挂帅）" />
          <div class="partBot">
            <div class="innerBox boderOne">
              <div class="ltxt">揭榜数量</div>
              <div class="numberTwo">
                <div v-for="(item, i) in jsgg.jbsl" :key="i">
                  <div class="numtxt" v-if="item != ',' && item != '.' && item != '-'">
                    <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                  </div>
                  <div class="numtxt" v-else>{{ item }}</div>
                </div>
                <span class="numtxt" style="font-size: 32px; margin-top: 64px">个</span>
              </div>
            </div>
            <div class="innerBox boderOne">
              <div class="ltxt">揭榜总金额</div>
              <div class="numberTwo">
                <div v-for="(item, i) in jsgg.jbzje" :key="i">
                  <div class="numtxt" v-if="item != ',' && item != '.' && item != '-'">
                    <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                  </div>
                  <div class="numtxt" v-else>{{ item }}</div>
                </div>
                <span class="numtxt" style="font-size: 32px; margin-top: 64px">亿</span>
              </div>
            </div>
            <div class="innerBox">
              <div class="ltxt">揭榜率</div>
              <div class="numberTwo">
                <div v-for="(item, i) in jsgg.jbl" :key="i">
                  <div class="numtxt" v-if="item != ',' && item != '.' && item != '-'">
                    <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                  </div>
                  <div class="numtxt" v-else>{{ item }}</div>
                </div>
                <span class="numtxt" style="font-size: 32px; margin-top: 64px">%</span>
              </div>
            </div>
          </div>
        </div>

        <div class="partOne">
          <SubTitle title="科创平台" />
          <div class="partBotTwo">
            <div
              :class="{ innerBox: true, boderOne: true, newBox: true, isPtActive: ptIndex == 0 }"
              @click="changePt(0)"
            >
              <div class="ltxt">行业级科创平台</div>
              <div class="numberTwo">
                <div v-for="(item, i) in String(ptArr[0].hyzs)" :key="i">
                  <div class="numtxtwo" v-if="item != ',' && item != '.'">
                    <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                  </div>
                  <div class="numtxtwo" v-else>{{ item }}</div>
                </div>
                <span class="numtxtwo" style="font-size: 32px; margin-top: 64px">个</span>
              </div>
            </div>

            <div
              :class="{ innerBox: true, boderOne: true, newBox: true, isPtActive: ptIndex == 1 }"
              @click="changePt(1)"
            >
              <div class="ltxt">企业级科创平台</div>
              <div class="numberTwo">
                <div v-for="(item, i) in String(ptArr[0].qyzs)" :key="i">
                  <div class="numtxt" v-if="item != ',' && item != '.'">
                    <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                  </div>
                  <div class="numtxt" v-else>{{ item }}</div>
                </div>
                <span class="numtxt" style="font-size: 32px; margin-top: 64px">个</span>
              </div>
            </div>
          </div>
        </div>

        <div class="partTwo chartThreeClass" id="chartThree"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import MainTitle from '@/components/MainTitle.vue'
import SubTitle from '@/components/SubTitle.vue'
export default {
  name: 'CylRight',
  components: { MainTitle, SubTitle },
  data() {
    return {
      qyIndex: 0,
      ptIndex: 0,
      zsArr: [{ zs: 0 }, { zs: 0 }, { zs: 0 }],
      ptArr: [{ hyzs: 0, qyzs: 0 }],
      gszcz: {
        value: '240.74',
        zs: '1.1',
      },
      cyzc: {
        zcsl: '-',
        dfqys: '-',
        zcdfje: '-',
      },
      rczy: {
        xxrc: '-',
        wsqyj: '-',
        gszldz: '-',
      },
      jsgg: {
        jbsl: '-',
        jbzje: '-',
        jbl: '-',
      },
      // 图表实例
      chartThreeInstance: null,
    }
  },

  created() {
    this.getData()
  },

  mounted() {
    // top.addEventListener('message', (e) => {
    //   console.log('子页面-右侧监听', e)
    // }, false)

    // 检查ECharts是否正确加载
    if (typeof echarts === 'undefined') {
      console.error('ECharts未正确加载')
      return
    }

    // 确保DOM完全渲染后再初始化图表
    this.$nextTick(() => {
      console.log('DOM渲染完成，开始初始化图表')
      console.log('ECharts版本:', echarts.version)

      this.getChartsTwo()
      this.changeQy()
      this.changePt()

      // 延迟调试，确保所有初始化完成
      setTimeout(() => {
        this.debugChartThree()
      }, 1000)
    })
  },

  beforeDestroy() {
    // 清理图表实例和事件监听器
    if (this.chartThreeInstance) {
      // 清理resize监听器
      if (this.chartThreeInstance._resizeHandler) {
        window.removeEventListener('resize', this.chartThreeInstance._resizeHandler)
      }
      this.chartThreeInstance.dispose()
      this.chartThreeInstance = null
    }
  },

  methods: {
    getData() {
      getCsdnInterface1('csdn_qyhx57').then((res) => {
        this.zsArr = res.data.data
      })

      getCsdnInterface1('qyhx_cyl_kcpt').then((res) => {
        this.ptArr[0].hyzs = res.data.data[1].sl
        this.ptArr[0].qyzs = res.data.data[0].sl
      })

      // getCsdnInterface1('csdn_qyhx68', { cyl_name: '100' }).then((res) => {
      //   console.log('规上企业总产值', res)
      //   this.gszcz.value = res.data.data[0].gsgyzcz
      //   this.gszcz.zs = res.data.data[0].gsgyzczzs
      // })

      getCsdnInterface1('qyhx_cyl_xxrs').then((res) => {
        this.rczy.xxrc = String(res.data.data[0].tjz)
        this.rczy.wsqyj = String(res.data.data[1].tjz)
        this.rczy.gszldz = String((res.data.data[2].tjz / 10000).toFixed(2))
      })

      getCsdnInterface1('qyhx_hqzc_zczs').then((res) => {
        this.cyzc.zcsl = res.data.data[0].tjz
      })

      getCsdnInterface1('qyhx_cyl_zcdfsj').then((res) => {
        this.cyzc.dfqys = res.data.data[0].dfqys
        this.cyzc.zcdfje = String((res.data.data[0].zcdfje / 100000000).toFixed(1))
      })

      getCsdnInterface1('qlzf_cyzy_jbgs').then((res) => {
        this.jsgg.jbsl = res.data.data[0].tjz
        this.jsgg.jbzje = String((Number(res.data.data[1].tjz) / 10000).toFixed(2))
        this.jsgg.jbl = res.data.data[2].tjz
      })
    },

    changePt(index = 0) {
      this.ptIndex = index
      let str = index == 0 ? '行业级' : '企业级'
      console.log('changePt调用，参数:', { index, str })

      getCsdnInterface1('qyhx_cyl_kcpt', { kcpt: str })
        .then((res) => {
          console.log('API响应数据:', res)

          if (!res || !res.data || !res.data.data || !Array.isArray(res.data.data)) {
            console.error('API数据格式错误:', res)
            this.getChartsThree([], [])
            return
          }

          let sort = res.data.data.map((el) => el.cylmc || '')
          let xData = res.data.data.map((el) => el.sl || 0)
          console.log('处理后的图表数据:', { sort, xData })

          this.getChartsThree(sort, xData)
        })
        .catch((error) => {
          console.error('获取科创平台数据失败:', error)
          // 显示空图表或默认数据
          this.getChartsThree(['暂无数据'], [0])
        })
    },

    changeQy(index = 0) {
      this.qyIndex = index
      let sort = ['婺城区', '金东区', '兰溪市', '东阳市', '义乌市', '永康市', '浦江县', '武义县', '磐安县']
      let sortData = [0, 0, 0, 0, 0, 0, 0, 0, 0]
      switch (this.qyIndex) {
        case 0:
          getCsdnInterface1('csdn_qyhx56', { qylx: '链主培育企业' }).then((res) => {
            let resData = res.data.data.slice(0, 11)
            let xData = resData.map((el) => {
              return el.zs
            })
            let yData = resData.map((el) => {
              return el.qx
            })
            this.getChartsOne(yData, xData)
          })
          break
        case 1:
          getCsdnInterface1('csdn_qyhx56', { qylx: '骨干企业' }).then((res) => {
            let resData = res.data.data.slice(0, 11)
            let xData = resData.map((el) => {
              return el.zs
            })
            let yData = resData.map((el) => {
              return el.qx
            })
            this.getChartsOne(yData, xData)
          })
          break
        case 2:
          getCsdnInterface1('csdn_qyhx56', { qylx: '星火企业' }).then((res) => {
            let resData = res.data.data.slice(0, 11)
            let xData = resData.map((el) => {
              return el.zs
            })
            let yData = resData.map((el) => {
              return el.qx
            })
            this.getChartsOne(yData, xData)
          })
          break
      }
    },

    getChartsOne(name = [], datas = []) {
      let myChart = echarts.init(document.getElementById('chartOne'))
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: 'rgba(9, 24, 48, 0.5)',
          borderColor: 'rgba(75, 253, 238, 0.4)',
          textStyle: {
            color: '#CFE3FC',
            fontSize: '36',
          },
          borderWidth: 1,
        },
        grid: {
          top: '20%',
          right: '0%',
          left: '10%',
          bottom: '14%',
        },
        xAxis: [
          {
            type: 'category',
            data: name, // ["婺城区", "金东区", "兰溪市", "东阳市", "义乌市", "永康市", "浦江县", "武义县", "磐安县"]
            axisLine: {
              lineStyle: {
                color: 'rgba(122, 163, 197, 1)',
              },
            },
            axisLabel: {
              // margin: 10,
              rotate: 30,
              interval: 0,
              color: '#fff',
              textStyle: {
                fontSize: 24,
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            name: '企业数量（家）',
            nameTextStyle: {
              color: '#fff',
              fontSize: 28,
              padding: [0, 0, 20, 0],
            },
            axisLabel: {
              formatter: '{value}',
              color: '#fff',
              textStyle: {
                fontSize: 28,
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#FFFFFF',
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(122, 163, 197, 0)',
              },
            },
          },
        ],
        series: [
          {
            type: 'bar',
            data: datas,
            barWidth: '20px',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(19, 194, 194, 1)', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'rgba(0,77,167,0)', // 100% 处的颜色
                    },
                  ],
                  false
                ),
                shadowColor: 'rgba(0,160,221,1)',
                shadowBlur: 4,
              },
            },
            label: {
              normal: {
                show: false,
              },
            },
          },
        ],
      }
      myChart.setOption(option, true)
    },

    getChartsTwo() {
      getCsdnInterface1('csdn_qyhx68', { cyl_name: '' }).then((res) => {
        // console.log('图表2数据', res)
        let dataArr = res.data.data.slice(0, 10)
        let valArr = dataArr.map((el) => el.gsgyzcz)
        let zsArr = dataArr.map((el) => el.gsgyzczzs)
        let myChart = echarts.init(document.getElementById('chartTwo'))
        let option = {
          tooltip: {
            show: true,
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
            backgroundColor: 'rgba(9, 24, 48, 0.5)',
            borderColor: 'rgba(75, 253, 238, 0.4)',
            textStyle: {
              color: '#CFE3FC',
              fontSize: '36',
            },
            borderWidth: 1,
            formatter: (data) => {
              let html = ``
              html = `<div>${data[0].name}</div>
                            <div>${data[0].marker} ${data[0].seriesName}: ${data[0].value}</div>
                            <div>${data[1].marker} ${data[1].seriesName}: ${data[1].value}%</div>
                        `
              return html
            },
          },
          grid: {
            top: '20%',
            right: '0%',
            left: '12%',
            bottom: '20%',
          },
          xAxis: [
            {
              type: 'category',
              data: [
                '新能源汽车及关键零部件',
                '智能光伏及新型储能',
                '电动工具',
                '纺织服装',
                '生物医药及植入性医疗器械',
                '磁性材料',
                '集成电路及信创',
                '电子化学品',
                '工业机床',
                '机器人',
              ],
              axisLine: {
                lineStyle: {
                  color: 'rgba(122, 163, 197, 1)',
                },
              },
              axisLabel: {
                margin: 20,
                formatter: (value, index) => {
                  if (value.length > 3) {
                    return value.substring(0, 3) + '...'
                  } else {
                    return value
                  }
                },
                rotate: -30,
                interval: 0, //设置为 1，表示『隔一个标签显示一个标签』
                color: '#fff',
                textStyle: {
                  fontSize: 28,
                },
              },
              axisTick: {
                show: false,
              },
            },
          ],
          yAxis: [
            {
              name: '产值分布（亿元）',
              nameTextStyle: {
                color: '#fff',
                fontSize: 28,
                padding: [0, 0, 20, 0],
              },
              axisLabel: {
                formatter: '{value}',
                color: '#fff',
                textStyle: {
                  fontSize: 28,
                },
              },
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: '#FFFFFF',
                },
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(122, 163, 197, 0)',
                },
              },
            },
            {
              show: false,
              type: 'value',
              name: '单位：%',
              axisLabel: {
                fontSize: 14,
                color: '#4e5969',
                padding: [0, 0, 0, 5],
              },
              splitLine: {
                show: false,
              },
            },
          ],
          series: [
            {
              name: '产值分布',
              type: 'bar',
              // data: [222.98, 291.32, 131.14, 244.34, 55.72, 65.55, 20.23, 16.3, 20.28, 20.17],
              data: valArr,
              barWidth: '20px',
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: 'rgba(0, 177, 255, 1)', // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: 'rgba(0,77,167,0)', // 100% 处的颜色
                      },
                    ],
                    false
                  ),
                  shadowColor: 'rgba(0,160,221,1)',
                  shadowBlur: 4,
                },
              },
              label: {
                normal: {
                  show: false,
                },
              },
            },
            {
              name: '产值增速',
              type: 'line',
              symbolSize: '0',
              smooth: false,
              itemStyle: {
                color: '#FF7D00',
              },
              lineStyle: {
                color: '#FF7D00',
              },
              yAxisIndex: 1,
              // data: [30, -27.5, 17.5, 10, -5.2, -7.4, 45.9, 3.5, 1.8, 6.9]
              data: zsArr,
            },
          ],
        }
        myChart.setOption(option, true)
      })
    },

    getChartsThree(name = [], datas = []) {
      console.log('getChartsThree调用，参数:', { name, datas })

      // 检查容器是否存在
      const container = document.getElementById('chartThree')
      if (!container) {
        console.error('chartThree容器不存在')
        return
      }

      // 检查容器尺寸
      const rect = container.getBoundingClientRect()
      console.log('chartThree容器尺寸:', rect)

      if (rect.width === 0 || rect.height === 0) {
        console.warn('chartThree容器尺寸为0，延迟初始化')
        setTimeout(() => this.getChartsThree(name, datas), 100)
        return
      }

      // 销毁已存在的图表实例
      if (this.chartThreeInstance) {
        this.chartThreeInstance.dispose()
      }

      // 初始化图表
      let myChart = echarts.init(container)
      this.chartThreeInstance = myChart

      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: 'rgba(9, 24, 48, 0.5)',
          borderColor: 'rgba(75, 253, 238, 0.4)',
          textStyle: {
            color: '#CFE3FC',
            fontSize: '36',
          },
          borderWidth: 1,
        },
        grid: {
          top: '20%',
          right: '0%',
          left: '10%',
          bottom: '25%',
        },
        xAxis: [
          {
            type: 'category',
            data: name, // ["婺城区", "金东区", "兰溪市", "东阳市", "义乌市", "永康市", "浦江县", "武义县", "磐安县"]
            axisLine: {
              lineStyle: {
                color: 'rgba(122, 163, 197, 1)',
              },
            },
            axisLabel: {
              // margin: 10,
              interval: 0,
              color: '#fff',
              textStyle: {
                fontSize: 24,
              },
              rotate: -30,
              formatter: (value, index) => {
                if (value.length > 3) {
                  return value.substring(0, 3) + '...'
                } else {
                  return value
                }
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            name: '单位（个）',
            nameTextStyle: {
              color: '#fff',
              fontSize: 28,
              padding: [0, 0, 20, 0],
            },
            axisLabel: {
              formatter: '{value}',
              color: '#fff',
              textStyle: {
                fontSize: 28,
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#FFFFFF',
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(122, 163, 197, 0)',
              },
            },
          },
        ],
        series: [
          {
            type: 'bar',
            data: datas,
            barWidth: '20px',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(231, 121, 48, 1)', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'rgba(231, 121, 48, 0)', // 100% 处的颜色
                    },
                  ],
                  false
                ),
                shadowColor: 'rgba(231, 121, 48, 1)',
                shadowBlur: 4,
              },
            },
            label: {
              normal: {
                show: false,
              },
            },
          },
        ],
      }

      try {
        myChart.setOption(option, true)
        console.log('chartThree图表渲染成功')

        // 添加窗口大小变化监听
        const resizeHandler = () => {
          if (myChart && !myChart.isDisposed()) {
            myChart.resize()
          }
        }
        window.addEventListener('resize', resizeHandler)

        // 保存resize处理器以便清理
        myChart._resizeHandler = resizeHandler
      } catch (error) {
        console.error('chartThree图表渲染失败:', error)
      }
    },

    // 调试方法：检查图表状态
    debugChartThree() {
      console.log('=== chartThree调试信息 ===')

      const container = document.getElementById('chartThree')
      console.log('容器元素:', container)

      if (container) {
        const rect = container.getBoundingClientRect()
        console.log('容器尺寸:', rect)
        console.log('容器样式:', window.getComputedStyle(container))
      }

      console.log('图表实例:', this.chartThreeInstance)
      console.log('ptIndex:', this.ptIndex)
      console.log('ptArr:', this.ptArr)

      if (this.chartThreeInstance) {
        console.log('图表是否已销毁:', this.chartThreeInstance.isDisposed())
        console.log('图表配置:', this.chartThreeInstance.getOption())
      }

      console.log('=== 调试信息结束 ===')
    },
  },
}
</script>

<style scoped lang="less">
* {
  margin: 0;
  padding: 0;
}

[v-cloak] {
  display: none;
}

.container {
  width: 2070px;
  height: 1932px;
  padding: 10px 55px 0px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  /* background-color: #081932; */
}

.con {
  width: 100%;
  /* height: 910px; */
  height: 50%;
  /* margin-bottom: 15px; */
  /* overflow: hidden; */
  display: flex;
  justify-content: space-between;
}

.con-item {
  width: 48%;
  overflow: hidden;
}

.item-content {
  width: 100%;
  height: 770px;
  position: relative;
  /* background-color: #a0e3ff; */
}

.title {
  width: 100%;
  text-align: center;
  font-size: 44px;
  font-style: italic;
  background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: absolute;
}

.moreClass {
  font-size: 36px;
  cursor: pointer;
}

.cyzyClass {
  display: flex;
  flex-direction: column;
}

.partOne {
  display: flex;
  flex-direction: column;
  margin-top: 20px;
}

.partTwo {
  width: 100%;
  flex: 1;
  margin-top: 20px;
  min-height: 300px; /* 确保容器有最小高度 */
}

.subTitle {
  width: 100%;
  height: 90px;
  background-image: url('@/pages/qyzfCyl/img/cyl/ejbt.png');
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40px;
  color: #ffffff;
  position: relative;
}

.partBot {
  width: 100%;
  height: 240px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  background-image: url('@/pages/qyzfCyl/img/cyl/subBg.png');
  background-size: 100% 100%;
}

.partBotTwo {
  width: 100%;
  height: 240px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  background-image: url('@/pages/qyzfCyl/img/cyl/subBg.png');
  background-size: 100% 100%;
}

.innerBox {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.boderOne {
  border-right: 2px solid;
  border-image: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0) 10%,
      rgba(0, 255, 200, 1) 50%,
      rgba(255, 255, 255, 0) 90%
    )
    2 2 2 2;
}

.ltxt {
  font-size: 36px;
  color: #ffffff;
}

.numtxt {
  font-size: 80px;
  letter-spacing: 1px;
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  font-weight: bolder;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-top: 36px;
}

.numtxtwo {
  font-size: 80px;
  letter-spacing: 1px;
  background: linear-gradient(180deg, #ffc460 0%, #fd852e 100%);
  font-weight: bolder;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-top: 36px;
}

.upClass {
  width: 100%;
  height: 100px;
  background-image: url('@/pages/qyzfCyl/img/cyl/lj.png');
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 30px 0 0 0;
}

.txtS {
  font-size: 32px;
  color: #ffffff;
}

.countCard {
  margin: 0 34px;
}

.number {
  display: inline-block;
  font-size: 40px;
  color: #fff;
  font-weight: 400;
}

.number .numbg {
  display: inline-block;
  width: 55px;
  height: 75px;
  line-height: 75px;
  text-align: center;
  background: url('@/pages/qyzfCyl/img/qyhx/num.png') no-repeat;
  background-size: contain;
  margin: 0 4px;
}

.numbg span {
  font-size: 64px;
  background: linear-gradient(180deg, #ffc460 0%, #fd852e 100%);
  font-weight: bolder;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.midClass {
  width: 100%;
  height: 235px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-column-gap: 20px;
  margin-bottom: 30px;
}

.cardS {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  cursor: pointer;
}

.cardBg {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: url('@/pages/qyzfCyl/img/cyl/ban.png');
  background-size: 100% 100%;
  animation: fadeIn 4s infinite linear;
}

.cardBgTwo {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: url('@/pages/qyzfCyl/img/cyl/ban2.png');
  background-size: 100% 100%;
}

.txtTwo {
  font-size: 36px;
  color: #ffffff;
  margin-top: 60px;
  z-index: 9;
}

.numTwo {
  font-size: 64px;
  letter-spacing: 1px;
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  font-weight: bolder;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.numberTwo {
  display: flex;
  align-items: center;
  /* font-size: 40px;
  color: #fff;
  font-weight: 400; */
  z-index: 9;
}

.numbgTwo span {
  font-size: 80px;
  letter-spacing: 1px;
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  font-weight: bolder;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.tbzzWrap {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 36px;
  box-sizing: border-box;
}

.tbzzL {
  color: #ffffff;
  font-size: 28px;
}

.tbzzR {
  font-size: 28px;
  color: #f7ba1e;
}

.charOneClass {
  width: 100%;
  flex: 1;
  /* background-color: #A0E3FF; */
}

.upTwoClass {
  width: 100%;
  height: 230px;
  background: linear-gradient(90deg, rgba(0, 121, 227, 0) 10%, rgba(0, 204, 227, 0.3) 50%, rgba(0, 121, 227, 0) 90%);
  margin: 29px 0px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.upTwoClassTwo {
  width: 100%;
  height: 230px;
  background: linear-gradient(90deg, rgba(0, 121, 227, 0) 10%, rgba(28, 148, 249, 0.3) 50%, rgba(0, 121, 227, 0) 90%);
  margin: 29px 0px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.upsClassTwo {
  display: flex;
  justify-content: center;
  align-items: center;
}

.upsClass {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.downClass {
  width: 100%;
  box-sizing: border-box;
  padding: 0 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 48px;
}

.twoClass {
  display: flex;
  align-items: center;
}

.tbLabel {
  font-size: 28px;
  color: #ffffff;
  margin-right: 24px;
}

.txtNum {
  font-size: 28px;
  color: #f7ba1e;
}

.chartTwoClass {
  width: 100%;
  flex: 1;
  /* background-color: #A0E3FF; */
}

.threeUpWrap {
  width: 100%;
  height: 230px;
  margin: 30px 0px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-column-gap: 12px;
}

.zsyzItem {
  width: 100%;
  height: 100%;
  /* background-image: url('/static/citybrain/qyzf/img/cyl/tai.png');
  background-size: 100% 100%; */
  display: flex;
  align-items: center;
  flex-direction: column;

  position: relative;
}

.zsyzBg {
  width: 100%;
  height: 100%;
  background-image: url('@/pages/qyzfCyl/img/cyl/tai.png');
  background-size: 100% 100%;
  animation: fadeIn 4s infinite linear;
  position: absolute;
}

.numThree {
  font-size: 80px;
  letter-spacing: 1px;
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  font-weight: bolder;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.txtThree {
  font-size: 36px;
  color: #ffffff;
  z-index: 999;
}

.chartThreeClass {
  width: 100%;
  flex: 1;
  /* background-color: #A0E3FF; */
}

@keyframes fadeIn {
  0% {
    /* width: 10px;
    height: 10px; */
    opacity: 0;
    /*初始状态 透明度为0*/
  }

  50% {
    /* width: 20px;
    height: 20px; */
    opacity: 1;
    /*中间状态 透明度为1*/
  }

  100% {
    /* width: 10px;
    height: 10px; */
    opacity: 0;
    /*结尾状态 透明度为0*/
  }
}

.newbottom {
  position: relative;
  top: -150px;
  /* border: 1px solid #FD852E; */
  height: 114%;
  display: flex;
  flex-direction: column;
}

.newBox {
  cursor: pointer;
}

.isPtActive {
  background-image: url('@/pages/qyzfCyl/img/cyl/new/ptBg.png');
  background-size: 100% 100%;
}

/*************************************** 单个产业链中间部分 ***************************************/
.unitNew {
  font-size: 28px;
}

.item-contents {
  width: 100%;
  height: 770px;
  position: relative;
}

.ht {
  /* background-color: #a0e3ff; */
  height: 800px;
}

.hs {
  /* background-color: #a0e3ff; */
  height: 830px;
}

.newh {
  height: 50% !important;
}

.dotClass {
  width: 60px;
  height: 90px;
  cursor: pointer;
}

.moneyBg {
  position: absolute;
  top: 26px;
  right: 66px;
  width: 40px;
  height: 44px;
  cursor: pointer;
}
</style>