<!--
 * @Description: 产业链右侧组件
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-09-18 14:08:20
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-25 14:00:10
-->
<template>
  <div class="fxyjfx_right">
    <MainTitle title="活力专题分析报告" size="large" />
    <div class="right3">
      <CommonTable
        :height="'410px'"
        :tableData="tableData2"
        style="margin-top: 16px"
        :showIndex="true"
        @infoClick="infoClick"
      ></CommonTable>
    </div>
    <MainTitle title="城市活力分析报告（年报）" size="large" />
    <div class="right1">
      <div class="filter_box">
        <el-select v-model="year" placeholder="年份">
          <el-option v-for="item in yearList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-select v-model="weekCountry" placeholder="区域">
          <el-option v-for="item in countryList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-input placeholder="请输入内容" v-model="weekInput" class="search_input" style="width: 648px">
          <el-button slot="append" class="search_btn">搜索</el-button>
        </el-input>
      </div>
      <CommonTable
        :height="'410px'"
        :tableData="tableData"
        style="margin-top: 16px"
        :showIndex="true"
        @infoClick="infoClick"
      ></CommonTable>
    </div>
    <MainTitle title="城市活力分析报告（月报）" size="large" />
    <div class="right2">
      <div class="filter_box">
        <el-select v-model="month" placeholder="月份">
          <el-option v-for="item in monthList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-select v-model="country" placeholder="区域">
          <el-option v-for="item in countryList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-input placeholder="请输入内容" v-model="monthInput" class="search_input" style="width: 648px">
          <el-button slot="append" class="search_btn">搜索</el-button>
        </el-input>
      </div>

      <CommonTable
        :height="'410px'"
        :tableData="tableData1"
        style="margin-top: 16px"
        :showIndex="true"
        @infoClick="infoClick"
      ></CommonTable>
    </div>
  </div>
</template>

<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import { getEncode64 } from '@/utils/index.js'
import MainTitle from '@/components/MainTitle.vue'
import CommonTable from './commonTable.vue'
export default {
  name: 'fxyjfxRight',
  components: { MainTitle, CommonTable },
  data() {
    return {
      //城市活力分析报告月报
      monthList: [
        { label: '1月', value: '1' },
        { label: '2月', value: '2' },
        { label: '3月', value: '3' },
        { label: '4月', value: '4' },
        { label: '5月', value: '5' },
        { label: '6月', value: '6' },
        { label: '7月', value: '7' },
        { label: '8月', value: '8' },
        { label: '9月', value: '9' },
        { label: '10月', value: '10' },
        { label: '11月', value: '11' },
        { label: '12月', value: '12' },
      ],
      month: '',
      countryList: [
        {
          label: '王义贞镇',
          value: 1,
        },
        {
          label: '孛畈镇',
          value: 2,
        },
        {
          label: '烟店镇',
          value: 3,
        },
        {
          label: '雷公镇',
          value: 4,
        },
        {
          label: '洑水镇',
          value: 5,
        },
        {
          label: '接官乡',
          value: 6,
        },
        {
          label: '赵棚镇',
          value: 7,
        },

        {
          label: '陈店乡',
          value: 8,
        },
        {
          label: '木梓乡',
          value: 9,
        },
        {
          label: '棠棣镇',
          value: 10,
        },
        {
          label: '李店镇',
          value: 11,
        },

        {
          label: '开发区',
          value: 12,
        },
        {
          label: '府城街道',
          value: 13,
        },
        {
          label: '南城街道',
          value: 14,
        },
        {
          label: '巡店镇',
          value: 15,
        },
        {
          label: '辛榨乡',
          value: 16,
        },
      ],
      country: '',
      monthInput: '',
      tableData: {
        thead: [
          { label: '年报名称', property: 'name', width: 1700, align: 'center' },
          { label: '操作', property: 'handle', width: 360, align: 'center' },
        ],
        tbody: [
          {
            name: '2025年安陆市人口分析报告',
            url: 'http://10.75.121.53:8081/profile/file/年度城市活力分析研判报告.doc',
          },
          {
            name: '2024年安陆市人口分析报告',
          },
          {
            name: '2023年安陆市人口分析报告',
          },
          {
            name: '2022年安陆市人口分析报告',
          },
        ],
      },
      //城市活力分析报告周报
      yearList: [
        { label: '2022年', value: '1' },
        { label: '2023年', value: '2' },
        { label: '2024年', value: '3' },
        { label: '2025年', value: '4' },
      ],
      year: '',
      weekCountry: '',
      weekInput: '',
      tableData1: {
        thead: [
          { label: '月报名称', property: 'name', width: 1700, align: 'center' },
          { label: '操作', property: 'handle', width: 360, align: 'center' },
        ],
        tbody: [
          {
            name: '2025年8月安陆市人口分析报告',
          },
          {
            name: '2025年7月安陆市人口分析报告',
          },
          {
            name: '2025年6月安陆市人口分析报告',
          },
          {
            name: '2025年5月安陆市人口分析报告',
          },
        ],
      },
      //活力专题分析报告
      tableData2: {
        thead: [
          { label: '报告名称', property: 'name', width: 1700, align: 'center' },
          { label: '操作', property: 'handle', width: 360, align: 'center' },
        ],
        tbody: [
          {
            name: '金华市2025年春节分析报告',
            url: 'http://10.75.121.53:8081/profile/file/安陆市春节期间综合研究报告-城市活力分析研判报告.doc',
          },
          {
            name: '安陆市2024春节人口报告',
          },
          {
            name: '安陆市2024国庆假期人口报告',
          },
          {
            name: '安陆市2024五一假期人口报告',
          },
        ],
      },
    }
  },

  created() {
    this.getData()
  },

  mounted() {},
  methods: {
    async infoClick(item, index) {
      console.log(item, index)
      if (item.url) {
        if (index == 1) {
          window.open(
            'http://10.75.121.53:8012/onlinePreview?url=' + encodeURIComponent(getEncode64(item.url)),
            '_blank'
          )
        } else {
          const fileName = item.name || this.getFileNameFromUrl(item.url) || '下载文件'
          await this.downloadFile(item.url, fileName)
        }
      } else {
        this.$message.warning('暂无文件')
      }
    },

    getFileNameFromUrl(url) {
      try {
        const decoded = decodeURIComponent(url)
        const parts = decoded.split('?')[0].split('/')
        return parts[parts.length - 1] || ''
      } catch (e) {
        return ''
      }
    },

    async downloadFile(url, fileName) {
      try {
        const response = await fetch(url, { credentials: 'include' })
        if (!response.ok) throw new Error('网络错误: ' + response.status)
        const blob = await response.blob()
        const href = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = href
        a.download = fileName
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(href)
      } catch (err) {
        console.warn('Blob 下载失败，尝试使用 a[download] 方式:', err)
        try {
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = url
          a.setAttribute('download', fileName)
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
        } catch (e2) {
          console.warn('a[download] 方式失败，使用窗口打开作为回退:', e2)
          window.open(url, '_blank')
        }
      }
    },
  },

  beforeDestroy() {},
}
</script>

<style scoped lang="less">
.fxyjfx_right {
  width: 2262px;
  .right1 {
    height: 550px;
  }
  .right2 {
    height: 550px;
  }
  .right3 {
    height: 460px;
  }
  .filter_box {
    display: flex;
    align-content: center;
    align-items: center;
    margin-top: 24px;
    /deep/.el-select {
      width: 210px;
      height: 75px;
      margin-left: 40px;
      background: rgba(0, 61, 61, 0.8);
      border-radius: 10px 10px 10px 10px;
      border: 2px solid #1a93b5;
      box-sizing: border-box;
      .el-input__inner {
        font-size: 36px !important;
        border: none !important;
        background: transparent;
        height: 75px;
        line-height: 75px !important;
      }
      .el-select__caret {
        font-size: 36px !important;
      }
      .el-input__icon {
        line-height: 75px !important;
        width: 40px !important;
      }
    }
    /deep/.search_input {
      height: 75px;
      margin-left: 40px;
      background: rgba(0, 61, 61, 0.8);
      border-radius: 10px 10px 10px 10px;
      border: 2px solid #1a93b5;
      .el-input__inner {
        font-size: 36px !important;
        border: none !important;
        background: transparent;
        height: 75px;
        line-height: 75px !important;
      }
      .el-input-group__append {
        border: none !important;
        background: transparent;
      }
      .search_btn {
        width: 125px;
        height: 75px;
        background: linear-gradient(360deg, #003e4f 0%, #38c2f0 100%);
        border: none;
        font-size: 33px;
        color: #fefefe;
        line-height: 48px;
      }
    }
  }
}
</style>
<style lang="less">
.el-select-dropdown__item {
  font-size: 28px !important;
}
</style>