<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-09-22 10:22:58
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-25 13:44:20
-->
<template>
  <div class="sub-title">
    {{ title }}
    <img v-if="showIcon" :src="iconSrc" class="title-icon" @click="handleIconClick" />
  </div>
</template>

<script>
import defaultIcon from '@/assets/img/dyCyl/zczcNew.png'
export default {
  name: 'SubTitle',
  props: {
    title: {
      type: String,
      required: true,
      default: '',
    },
    showIcon: {
      type: Boolean,
      default: false,
    },
    iconSrc: {
      type: String,
      default: defaultIcon,
    },
  },
  computed: {},
  methods: {
    handleIconClick() {
      this.$emit('icon-click')
    },
  },
}
</script>

<style lang="less" scoped>
.sub-title {
  width: 100%;
  height: 91px;
  background-image: url('@/assets/img/sub-title.png');
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40px;
  color: #ffffff;
  position: relative;
  &::before{
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    width: 53px;
    height: 28px;
    background-image: url('@/assets/img/sub-title-left.png');
    background-size: cover;
    z-index: -1;
  }
  &::after{
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 0;
    width: 53px;
    height: 28px;
    background-image: url('@/assets/img/sub-title-right.png');
    background-size: cover;
    z-index: -1;
  }
}

.title-icon {
  position: absolute;
  top: 26px;
  right: 66px;
  width: 40px;
  height: 44px;
  cursor: pointer;
}
</style>
