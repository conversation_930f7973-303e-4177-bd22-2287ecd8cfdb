<template>
  <div class="qyhx-left-container">
    <!-- 股东信息和知识产权 -->
    <div class="con">
      <div class="con-item">
        <MainTitle title="企业经营" />
        <!-- 企业经营 -->
        <div class="qyjj-box">
          <ul class="qyjj-ul">
            <li v-for="(item, index) in qyjjData" :key="index">
              <img :src="require('@/pages/qyzf/img/qyjj' + index + '.png')" alt="" />
              <div>
                <p class="name-css">{{ qyjjTime }}{{ item.name }}</p>
                <p>
                  <span class="zscp-num s-c-grey-gradient" style="font-size: 40px">
                    {{ item.value }}
                    <span style="font-size: 26px">万元</span>
                  </span>
                </p>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div class="con-item">
        <MainTitle title="企业纳税" />
        <!-- 企业纳税 -->
        <div style="position: relative">
          <div id="qyls" style="width: 100%; height: 700px"></div>
          <div v-if="cwxx.length == 0" style="position: absolute; top: 160px; width: 100%" class="no-data">
            暂无数据
          </div>
        </div>
      </div>
    </div>
    <!-- 政策申报和法人信用 -->
    <div class="con">
      <div class="con-item">
        <MainTitle title="企业用工">
          <template #right>
            <ul class="title-item">
              <li
                v-for="(item, index) in qyygType"
                :key="index"
                :class="[qyygTypeIndex == index ? 'title-item-active' : '']"
                @click="qyygTogType(index, gmData)"
                style="z-index: 999; cursor: pointer"
              >
                {{ item }}
              </li>
            </ul>
          </template>
        </MainTitle>
        <!-- 政策申报 -->
        <div class="zcsb-type" style="margin-top: 24px">
          <div class="qiyyg-sum">
            员工总数 :
            <span class="sum">{{ personAll }}</span>
          </div>
        </div>
        <div class="qyyg-box" style="position: relative">
          <div
            v-show="personAll == 0 && qyygTypeIndex == 0"
            style="position: absolute; top: 160px; width: 100%"
            class="no-data"
          >
            暂无数据
          </div>
          <div
            v-show="personAll != 0 || qyygTypeIndex != 0"
            id="qyhx-left-chart01"
            key="qyhx-left-chart01"
            style="width: 940px; height: 680px"
          ></div>
        </div>
      </div>
      <div class="con-item">
        <MainTitle title="政策兑付">
          <template #right>
            <div class="zctj" @click="openZctj" style="z-index: 999">政策推荐</div>
          </template>
        </MainTitle>
        <!-- 政策申报 -->
        <div class="table-box" style="margin-top: 24px">
          <div class="table">
            <div class="th">
              <div class="td2" style="width: 30%">申报时间</div>
              <div class="td2" style="width: 50%">政策名称</div>
              <div class="td2" style="width: 20%">兑付情况</div>
            </div>
            <div class="tableCon" v-if="zcTrData.length !== 0">
              <div class="line" v-for="(item, index) in zcTrData" :key="index">
                <div class="td2" style="width: 30%">{{ item.gskssj }}</div>
                <div class="td2" style="width: 50%">{{ item.xmmc }}</div>
                <div class="td2" style="width: 20%" :class="item.dfqkText == '已兑付' ? 'dfBtn' : ''">
                  {{ item.dfqkText }}
                </div>
              </div>
            </div>
            <div v-if="zcTrData.length == 0" class="no-data" style="margin-top: 300px">暂无数据</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 政策推荐弹窗 -->
    <div v-if="visible">
      <qyhxPolicy
        :visible="visible"
        :qyhxGsbq="qyhxGsbq"
        :qyhxJbxxList="qyhxJbxxList"
        @close="visible = false"
      ></qyhxPolicy>
    </div>
  </div>
</template>
<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import qyhxPolicy from '@/pages/qyzf/components/qyhxPolicyDialog'
import MainTitle from '@/components/MainTitle.vue'
export default {
  components: { qyhxPolicy, MainTitle },
  props: {
    allMessage: {
      type: Array,
      default: () => [],
    },
    qyhxGsbq: {
      type: [Array, String],
      default: '',
    },
    qyhxJbxxList: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      // 企业经营
      qyjjTime: '2022',
      personAll: 0,
      qyjjData: [
        {
          name: '年纳税金额',
          value: '--',
          unit: '万',
          tb: '--',
        },
        {
          name: '年企业销售总额',
          value: '--',
          unit: '万',
          tb: '--',
        },
        {
          name: '年利润总额',
          value: '--',
          unit: '万',
          tb: '--',
        },
        {
          name: '年资产总计',
          value: '--',
          unit: '万',
          tb: '--',
        },
        {
          name: '年负债总计',
          value: '--',
          unit: '万',
          tb: '--',
        },
      ],
      // 政策申报
      zcTypeIndex: 0,
      zcType: ['全部', '已兑付', '审核中', '已驳回'],
      zcThData: ['申报时间', '政策名称', '兑付情况'],
      zcTrData: [],
      zcLabel: ['gskssj', 'xmmc', 'dfqk'],
      allZcTrData: [],
      ydfZcData: [],
      shzZcData: [],
      ybhZcData: [],
      // 股东信息
      gdThData: ['股东', '持股比例(%)', '认缴金额(万元)'],
      gdTrData: [],
      gdLabel: ['mcgdxx_tzr', 'czbl', 'rjcze'],
      // 年龄分布
      nljgData: [
        {
          name: '18-30岁',
          value: '0',
          unit: '人',
        },
        {
          name: '30-40岁',
          value: '0',
          unit: '人',
        },
        {
          name: '45-50岁',
          value: '0',
          unit: '人',
        },
        {
          name: '50-60岁',
          value: '0',
          unit: '人',
        },
        {
          name: '60岁以上',
          value: '0',
          unit: '人',
        },
      ],
      // 企业用工
      qyygType: ['社保缴纳情况', '学历结构', '年龄结构'],
      qyygTypeIndex: 0,
      // 规模信息
      gmData: [],
      // allMessage: [],
      cwxx: [],
      visible: false,
    }
  },

  mounted() {
    this.initApi()
  },
  methods: {
    initApi() {
      let _this = this
      _this.cwxx = []
      if (this.allMessage.length == 0) {
        _this.qyygTogType(0, _this.gmData)
      }
      getCsdnInterface1('qyhx_qyxx_basic_al', { tyshxydm: this.allMessage[0].tyshxydm }).then((allData) => {
        console.log('allData', allData)
        if (allData.data.data == undefined || allData.data.data.length == 0) {
          _this.gdTrData = []
          _this.gmData = []
          _this.cwxx = []
          _this.qyygTogType(0, [])
        } else {
          let res = allData.data.data
          let df_state = [
            '待提交',
            '申报中',
            '审核中',
            '已作废',
            '',
            '',
            '',
            '关闭',
            '退回修改',
            '已兑付',
            '储备',
            '立项',
            '退回申报人',
          ]
          _this.gdTrData = res[0].gdXx
          _this.gmData = res[0].qygmXx
          _this.cwxx = res[0].cwXx
          console.log('这里', res, res[0].cwXx)
          if (res[0].cwXx) {
            _this.lineCharts('qyls', res[0].cwXx)
          }
          _this.qyygTogType(0, _this.gmData)
          _this.qyjjGetData(res[0].cwXx)
          if (res[0].jyghnhqxmXx) {
            _this.allZcTrData = res[0].jyghnhqxmXx.map((o) => {
              let hdje = df_state[o.zt]
              let css = o.zt == 9 ? 'ydf' : o.zt == 12 ? 'ybh' : o.zt == 1 ? 'shz' : ''
              o.dfqk = {
                formatter: `<span class="${css}">${hdje}</span>`,
              }
              o.dfqkText = hdje

              return o
            })
          }
          _this.zcTrData = _this.allZcTrData
        }
      })
    },
    // 企业用工切换
    qyygTogType(index, data) {
      this.qyygTypeIndex = index
      console.log(index)
      if (index == 0) {
        if (data != '') {
          let arr = data
          let data1 = arr
            .map((item) => {
              return item.yljfrs
            })
            .slice(0, 12)
            .reverse()
          let time = arr
            .map((item) => {
              return item.sjqb
            })
            .slice(0, 12)
            .reverse()

          this.personAll = data1[data1.length - 1]
          this.lineCharts2('qyhx-left-chart01', data1, time)
        }
      } else if (index == 1) {
        this.drawCharts1('qyhx-left-chart01')
      } else {
        this.getChart1('qyhx-left-chart01', this.nljgData)
      }
    },
    // 计算企业经营
    qyjjGetData(data) {
      let fullYear = new Date().getFullYear()
      let oldYear = fullYear - 1
      let twoYear = fullYear - 2
      this.qyjjData[0].value =
        Number(data[0].nsze.split('.')[0]).toLocaleString() + '.' + data[0].nsze.split('.')[1].slice(0, 1)
      this.qyjjData[1].value =
        Number(data[0].xs_yysr.split('.')[0]).toLocaleString() + '.' + data[0].xs_yysr.split('.')[1].slice(0, 1)
      this.qyjjData[2].value =
        Number(data[0].lrze.split('.')[0]).toLocaleString() + '.' + data[0].lrze.split('.')[1].slice(0, 1)
      this.qyjjData[3].value =
        Number(data[0].zcze.split('.')[0]).toLocaleString() + '.' + data[0].zcze.split('.')[1].slice(0, 1)
      this.qyjjData[4].value =
        Number(data[0].fzze.split('.')[0]).toLocaleString() + '.' + data[0].fzze.split('.')[1].slice(0, 1)

      if (data[0].nbnd == oldYear && data[1].nbnd == twoYear) {
        let newData = data[0]
        let oldData = data[1]
        this.qyjjTime = data[0].nbnd
        this.qyjjData[0].tb = this.countFun(data[0].nsze, data[1].nsze)
        this.qyjjData[1].tb = this.countFun(data[0].xs_yysr, data[1].xs_yysr)
        this.qyjjData[2].tb = this.countFun(data[0].lrze, data[1].lrze)
        this.qyjjData[3].tb = this.countFun(data[0].zcze, data[1].zcze)
        this.qyjjData[4].tb = this.countFun(data[0].fzze, data[1].fzze)
      }
    },
    countFun(data, data1) {
      let num = 0
      let num1 = 0
      if (data.indexOf('-') != -1) {
        let a = data.replace('-', '')
        let b = '-' + Number(a.split('.')[0] + '.' + a.split('.')[0].slice(0, 1))
        num = Number(b)
      } else {
        num = Number(data.split('.')[0] + '.' + data.split('.')[0].slice(0, 1))
      }
      if (data1.indexOf('-') != -1) {
        let a = data1.replace('-', '')
        let b = '-' + Number(a.split('.')[0] + '.' + a.split('.')[0].slice(0, 1))
        num1 = Number(b)
      } else {
        num1 = Number(data1.split('.')[0] + '.' + data1.split('.')[0].slice(0, 1))
      }
      let sum = (((num - num1) / num1) * 100).toFixed(1)
      // let sum = ((num / num1) * 100).toFixed(1)
      let tb = data1.indexOf('-') != -1 || num1 == 0 || sum == 'NaN' ? '' : sum.indexOf('-') != -1 ? sum : '+' + sum
      // let tb = data.indexOf('-') != -1 && num < num1 ? '-' + sum : '+' + sum
      return tb
    },
    zcsbTogType(type, index) {
      this.zcTypeIndex = index
      if (type.indexOf('已兑付') != -1) {
        this.zcTrData = this.allZcTrData.filter((ele) => ele.dfqk.formatter.indexOf('已兑付') != -1)
      } else if (type.indexOf('审核中') != -1) {
        this.zcTrData = this.shzZcData
      } else if (type.indexOf('已驳回') != -1) {
        this.zcTrData = this.ybhZcData
      } else {
        this.zcTrData = this.allZcTrData
      }
    },
    //规上产业和中小微企业
    lineCharts(id, data) {
      let myChart = this.$echarts.init(document.getElementById(id))
      let xdata = []
      let ydata = []
      data.map((ele) => {
        xdata.push(ele.nbnd.split(' ')[0])
        ydata.push(ele.nsze)
      })
      var fontColor = '#30eee9'
      let option = {
        grid: {
          left: '5%',
          right: '3%',
          top: '20%',
          bottom: '7%',
          containLabel: true,
        },
        tooltip: {
          show: true,
          trigger: 'item',
          confine: true,
          textStyle: {
            color: 'rgba(212, 232, 254, 1)',
            fontSize: 28,
          },
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          axisPointer: {
            lineStyle: {
              color: 'rgba(11, 208, 241, 1)',
              type: 'slider',
            },
          },
        },
        legend: {
          show: true,
          itemGap: 50,
          // icon: 'stack',
          top: 50,
          itemWidth: 60,
          itemHeight: 20,
          textStyle: {
            color: '#fff',
            fontSize: 30,
          },
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLabel: {
              margin: 20,
              rotate: 30,
              color: '#fff',
              fontSize: 30,
              interval: 0,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#fff',
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: '#fff',
              },
            },
            data: xdata.reverse(),
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位:万元',
            nameTextStyle: {
              color: '#fff',
              fontSize: 24,
              padding: [0, 20, 10, 0],
            },
            axisLabel: {
              textStyle: {
                color: '#fff',
                fontSize: 30,
              },
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(0, 192, 255, 0.2)',
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(0, 192, 255, 0.2)',
              },
            },
          },
        ],
        series: [
          {
            name: '企业纳税总额',
            type: 'line',
            symbol: 'circle',
            smooth: true,
            symbolSize: 15,
            itemStyle: {
              normal: {
                color: '#0092f6',
                lineStyle: {
                  color: '#0092f6',
                  width: 2,
                },
              },
            },
            markPoint: {
              itemStyle: {
                normal: {
                  color: 'red',
                },
              },
            },
            data: ydata.reverse(),
          },
        ],
      }
      myChart.setOption(option, true)
      tools.loopShowTooltip(myChart, option, { loopSeries: true })
      // myChart.getZr().on('mousemove', (param) => {
      //   myChart.getZr().setCursorStyle('default')
      // })
    },
    lineCharts2(id, data, time) {
      this.$echarts.init(document.getElementById(id)).dispose()
      let myChart = this.$echarts.init(document.getElementById(id))

      var fontColor = '#30eee9'

      let option = {
        grid: {
          left: '8%',
          right: '3%',
          top: '20%',
          bottom: '7%',
          containLabel: true,
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          confine: true,
          textStyle: {
            color: 'rgba(212, 232, 254, 1)',
            fontSize: 28,
          },
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          axisPointer: {
            lineStyle: {
              color: 'rgba(11, 208, 241, 1)',
              type: 'slider',
            },
          },
        },
        legend: {
          show: data.length > 0 ? true : false,
          itemGap: 50,
          // icon: 'stack',
          top: 50,
          itemWidth: 60,
          itemHeight: 20,
          textStyle: {
            color: '#fff',
            fontSize: 30,
          },
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLabel: {
              rotate: 45,
              color: '#fff',
              fontSize: 30,
              interval: 0,
              margin: 20,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#fff',
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: '#fff',
              },
            },
            data: time,
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: data.length > 0 ? '单位:人' : '',
            nameTextStyle: {
              color: '#fff',
              fontSize: 24,
              padding: [0, 20, 10, 0],
            },
            min: 0,
            // max: 1000,
            axisLabel: {
              textStyle: {
                color: '#fff',
                fontSize: 30,
              },
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(0, 192, 255, 0.2)',
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(0, 192, 255, 0.2)',
              },
            },
          },
        ],
        series: [
          {
            name: '社保缴纳情况',
            type: 'line',
            symbol: 'circle',
            smooth: true,
            symbolSize: 15,
            itemStyle: {
              normal: {
                color: '#0092f6',
                lineStyle: {
                  color: '#0092f6',
                  width: 2,
                },
              },
            },
            markPoint: {
              itemStyle: {
                normal: {
                  color: 'red',
                },
              },
            },
            data: data,
          },
        ],
      }
      myChart.setOption(option, true)
      tools.loopShowTooltip(myChart, option, { loopSeries: true })
      // myChart.getZr().on('mousemove', (param) => {
      //   myChart.getZr().setCursorStyle('default')
      // })
    },
    getChart1(id, data) {
      let MyEchart = this.$echarts.init(document.getElementById(id))
      let color = ['#ff9b58', '#00c0ff', '#22e8e8', '#ffc460', '#a9db52']
      let imgUrl = require('@/pages/qyzf/img/echarts-bg.png')
      let echartData = data
      let option = {
        color: color,
        legend: {
          show: true,
          top: 'center',
          left: '70%',
          // data: arrName,
          itemWidth: 20,
          itemHeight: 20,
          width: 50,
          padding: [0, 5],
          itemGap: 25,
          formatter: function (name) {
            var data = option.series[0].data //获取series中的data
            var total = 0
            var tarValue
            for (var i = 0, l = data.length; i < l; i++) {
              total += parseInt(data[i].value)
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            var p = ((tarValue / total) * 100).toFixed(2)
            return '{title|' + name + '}    {value|' + tarValue + '人' + '}'
            // return '{name|' + name + '}   {value|' + p + '%}'
          },
          textStyle: {
            rich: {
              title: {
                fontSize: 28,
                lineHeight: 10,
                color: '#fff',
              },
              value: {
                fontSize: 28,
                lineHeight: 18,
                color: '#FFC460',
              },
            },
          },
        },
        tooltip: {
          show: true,
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '24',
          },
          trigger: 'item',
          formatter: '{b}:{c}({d}%)',
        },
        graphic: [
          {
            z: 4,
            type: 'image',
            id: 'logo',
            left: '8%',
            top: '16.5%',
            z: -10,
            bounding: 'raw',
            rotation: 0, //旋转
            origin: [-30, 30], //中心点
            scale: [1.5, 1.5], //缩放
            style: {
              image: imgUrl,
              opacity: 1,
            },
          },
        ],
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['35%', '50%'],
            data: echartData,
            hoverAnimation: true,
            label: {
              show: false,
            },
            itemStyle: {
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
            },
          },
        ],
      }
      MyEchart.setOption(option, true)
      MyEchart.getZr().on('mousemove', (param) => {
        MyEchart.getZr().setCursorStyle('default')
      })
    },
    drawCharts1(id, echartData) {
      let myChart = this.$echarts.init(document.getElementById(id))
      let imgUrl = require('@/pages/qyzf/img/xljg-bg.png')
      let data = [
        {
          name: '本科及以上',
          value: 0,
        },
        {
          name: '大专',
          value: 0,
        },
        {
          name: '中专',
          value: 0,
        },
        {
          name: '小学',
          value: 0,
        },
      ]
      let arrName = data.map((x) => x.name)
      let arrValue = data.map((x) => x.value)
      let sumValue = eval(arrValue.join('+'))
      let objData = array2obj(data, 'name')
      let optionData = getData(data)

      function array2obj(array, key) {
        var resObj = {}
        for (var i = 0; i < array.length; i++) {
          resObj[array[i][key]] = array[i]
        }
        return resObj
      }

      function getData(data) {
        var res = {
          series: [],
          yAxis: [],
        }
        for (let i = 0; i < data.length; i++) {
          res.series.push({
            name: '学历',
            type: 'pie',
            clockWise: false, //顺时加载
            hoverAnimation: false, //鼠标移入变大
            radius: [0, 0],
            // radius: [62 - i * 15 + '%', 52 - i * 15 + '%'],
            center: ['28%', '55%'],
            label: {
              show: false,
            },
            itemStyle: {
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
              borderWidth: 5,
            },
            data: [
              {
                value: data[i].value,
                name: data[i].name,
              },
              {
                value: 0,
                // value: sumValue - data[i].value * 2,
                name: '',
                itemStyle: {
                  color: 'rgba(0,0,0,0)',
                  borderWidth: 0,
                },
                tooltip: {
                  show: false,
                },
                hoverAnimation: false,
              },
            ],
          })
          res.series.push({
            name: '',
            type: 'pie',
            silent: true,
            z: 1,
            clockWise: false, //顺时加载
            hoverAnimation: false, //鼠标移入变大
            radius: [62 - i * 15 + '%', 52 - i * 15 + '%'],
            center: ['28%', '55%'],
            label: {
              show: false,
            },
            itemStyle: {
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
              borderWidth: 5,
            },
            data: [
              {
                value: 7.5,
                itemStyle: {
                  color: '#003B4F',
                  borderWidth: 0,
                },
                tooltip: {
                  show: false,
                },
                hoverAnimation: false,
              },
              {
                value: 2.5,
                name: '',
                itemStyle: {
                  color: 'rgba(0,0,0,0)',
                  borderWidth: 0,
                },
                tooltip: {
                  show: false,
                },
                hoverAnimation: false,
              },
            ],
          })
          res.yAxis.push(((data[i].value / sumValue) * 100).toFixed(2) + '%')
        }
        return res
      }

      let option = {
        // backgroundColor: "#fff",
        legend: {
          show: true,
          top: 'center',
          left: '60%',
          data: arrName,
          itemWidth: 20,
          itemHeight: 20,
          width: 50,
          icon: 'circle',
          padding: [0, 5],
          itemGap: 25,
          formatter: function (name) {
            return '{title|' + name + '}    {value|' + objData[name].value + '人}'
          },
          textStyle: {
            rich: {
              title: {
                fontSize: 28,
                lineHeight: 10,
                color: '#fff',
              },
              value: {
                fontSize: 28,
                lineHeight: 18,
                color: '#FFC460',
              },
            },
          },
        },
        graphic: [
          {
            z: 4,
            type: 'image',
            id: 'logo',
            left: '0.9',
            top: '19.5%',
            z: -10,
            bounding: 'raw',
            rotation: 0, //旋转
            origin: [-30, 30], //中心点
            scale: [1.3, 1.3], //缩放
            style: {
              image: imgUrl,
              opacity: 1,
            },
          },
        ],
        tooltip: {
          show: true,
          trigger: 'item',
          formatter: '{a}<br>{b}:{c}({d}%)',
          textStyle: {
            color: 'rgba(212, 232, 254, 1)',
            fontSize: 28,
          },
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
        },
        color: ['#00c0ff', '#22e8e8', '#ffc460', '#a9db52'],
        grid: {
          top: '20%',
          bottom: '48%',
          left: '20%',
          containLabel: false,
        },
        yAxis: [
          {
            type: 'category',
            inverse: true,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
              interval: 0,
              inside: true,
              textStyle: {
                color: '#fff',
                fontSize: 20,
              },
            },
            data: optionData.yAxis,
          },
        ],
        xAxis: [
          {
            show: false,
          },
        ],
        series: optionData.series,
      }

      myChart.setOption(option, true)
      // tools.loopShowTooltip(myChart, option, { loopSeries: true })
    },
    openZctj() {
      this.visible = true
    },
  },
}
</script>

<style lang="less" scoped>
/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 5px;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
  border-radius: 5px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(35, 144, 207, 0.4);
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
.qyhx-left-container {
  width: 2070px;
  height: 1904px;
  padding: 10px 55px 30px;
  box-sizing: border-box;
  /* background: #05163066; */
  /* background: #000; */
}

.con {
  width: 100%;
  height: 880px;
  margin-bottom: 30px;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
}

.con > div {
  width: 48%;
}

.table-th {
  height: 92px !important;
  background-color: #0ab7ff1a !important;
  margin-bottom: 0 !important;
}

.th {
  line-height: 92px !important;
}

.th,
.td {
  color: #cde7ff !important;
}

.table-tr {
  height: calc(100% - 60px) !important;
}

.tr {
  height: 92px;
  box-sizing: border-box;
  /* padding: 20px 0 !important; */
  margin-bottom: 0 !important;
  border-bottom: 1px solid transparent;
}

.tr:first-child {
  border-top: 1px solid transparent;
  /* border-top: 1px solid #556a8463; */
}

.tr:hover {
  background-color: #3e74aa70 !important;
}

.tr:nth-child(odd) {
  background: #0ab7ff00 !important;
}

.tr:nth-child(even) {
  background: #0ab7ff1a !important;
}

.td {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

/* 知识产权 */
.zscq-img {
  width: 100%;
}

.zscq-ul {
  font-size: 32px;
  color: #fff;
  display: flex;
  justify-content: space-around;
}

.zscq-ul > li {
  width: 280px;
  height: 250px;
  /* background-image: url('/static/citybrain/qyhx/images/xq/zdcp-bg.png'); */
  background-size: 100% auto;
  text-align: center;
}

.zscq-ul > li > p:first-child {
  margin-top: 35px;
}

.zscq-ul > li > p:last-child {
  margin-top: 90px;
}

.zscp-num {
  font-size: 54px;
  font-weight: bold;
}

.zscq-unit {
  font-size: 30px;
}

/* 政策申报 */
/* .table-box .th:first-child {
  flex: 0.8 !important;
}

.table-box .th:last-child {
  flex: 0.5 !important;
}

.table-box .td:first-child {
  flex: 0.8 !important;
}

.table-box .td:nth-child(2) div {
  width: 410px !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
  overflow: hidden;
}

.table-box .td:last-child {
  flex: 0.5 !important;
} */

.zcsb-type {
  width: 100%;
  margin-bottom: 10px;
  position: relative;
}

.qiyyg-sum {
  position: absolute;
  font-size: 32px;
  color: #fff;
}

.sum {
  font-size: 50px;
  color: #22e8e8;
}

.zcsb-ul {
  width: 100%;
  color: #fff;
  font-size: 32px;
  display: flex;
  justify-content: center;
}

.zcsb-ul > li {
  width: 200px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  margin: 0 15px;
  padding: 2px 0;
  background: #1c2a47;
  border-radius: 4px;
}

.zcsb-type-active {
  background: #1b63ae !important;
}

/* 企业经营 */
.qyjj-box {
  width: 100%;
  height: 700px;
  overflow: hidden;
}

.qyjj-ul {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
  justify-content: space-between;
}

.qyjj-ul > li {
  width: 50%;
  font-size: 28px;
  color: #fff;
  display: flex;
}

.qyjj-ul > li > div {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.name-css {
  font-size: 32px;
  position: relative;
  margin-bottom: 20px;
}

.name-css::after {
  content: '';
  width: 100%;
  height: 25px;
  background: radial-gradient(#0ab5ffd5 0%, #00000000 90%, #00000000 100%);
  opacity: 0.5;
  position: absolute;
  bottom: -30px;
  left: 0;
}

.qyjj-ul > li img {
  width: 160px;
}

/* 企业用工 */
.qyyg-ul {
  width: 100%;
  font-size: 32px;
  color: #fff;
  display: flex;
  justify-content: flex-end;
  font-style: italic;
  padding-right: 20px;
  box-sizing: border-box;
  margin-bottom: 20px;
}

.qyyg-ul > li {
  margin: 0 10px;
  padding: 5px 0;
  border-bottom: 5px solid transparent;
}

.qyyg-type-active {
  border-color: #177de8 !important;
}

.no-data {
  font-size: 50px;
  color: #dcefffd5;
  text-align: center;
  margin-top: 200px;
  position: relative;
}

.no-data::before {
  content: '';
  width: 157px;
  height: 148px;
  background-image: url('@/pages/qyzf/img/no-data.png');
  position: absolute;
  top: -170px;
  margin-left: 21px;
}

/* 标题 */
.first-title {
  position: relative;
  width: 100%;
  height: 95px;
  background-image: url('@/pages/qyzf/img/first-title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-left: 100px;
  padding-right: 50px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0;
  line-height: 110px;
  font-size: 45px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
}

.first-title > i,
.two-title > i {
  position: absolute;
  width: 1062px;
  height: 90px;
  display: inline-block;
  background: url('@/pages/qyzf/img/line.png');
  background-size: 100%;
  animation-name: animateLine;
  animation-duration: 10s;
  animation-iteration-count: infinite;
  /* animation-direction:alternate; */
  /* animation-delay */
}

.two-title {
  position: relative;
  width: 100%;
  height: 95px;
  background-image: url('@/pages/qyzf/img/two-title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-left: 100px;
  padding-right: 50px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0;
  line-height: 110px;
  font-size: 45px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
}

.title-text {
  line-height: 110px;
  font-size: 50px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
  position: relative;
}

.flag-click::after {
  content: '';
  width: 26px;
  height: 23px;
  /* background-image: url('/static/images/common/header/click-1.png'); */
  position: absolute;
  top: 50px;
  margin-left: 10px;
}

.title-item {
  font-size: 32px;
  color: rgba(255, 255, 255, 0.843);
  display: flex;
}

.title-item > li {
  margin-right: 30px;
}

.title-item > li:last-child {
  margin-right: 0;
}

.title-item-active {
  color: #fff;
}

/* 政策兑付 */
.ydf {
  color: #0bfffe;
  background: #0bfffe33;
  border-radius: 4px;
  padding: 2px 20px;
}

.shz {
  color: #fd8830;
  background: #fd883033;
  border-radius: 4px;
  padding: 2px 20px;
}

.ybh {
  color: #ff4949;
  background: #ff494933;
  border-radius: 4px;
  padding: 2px 20px;
}

.zctj {
  width: 160px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  cursor: pointer;
  font-size: 30px;
  color: #fff;
  background-color: #3291f8;
  border-radius: 5%;
  margin-top: 16px;
}

.table {
  color: #cde7ff;
  width: 100%;
  overflow: unset;
  font-size: 30px;
}

.table .th {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #0ab7ff1a;
  width: 98%;
  height: 92px;
  font-size: 30px;
}

.table .th .td2,
.table .line .td2 {
  text-align: center;
  padding: 12px 0;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table .line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 98%;
  height: 92px;
  font-size: 32px;
}

.table .line:nth-child(2n + 2) {
  background-color: #0ab7ff1a;
}

.table .dfBtn {
  color: #0bfffe;
  background: #0bfffe33;
  border-radius: 4px;
}

.tableCon {
  height: 800px;
  overflow-y: scroll;
}
</style>
