<template>
  <div class="chart-item">
    <SubTitle title="养老保险情况" />
    <div class="chart-container">
      <div id="pensionInsuranceStatus" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'PensionInsuranceStatus',
  components: { SubTitle },
  data() {
    return {
      chartData: [],
      loading: false,
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      try {
        this.loading = true
        // 获取 SubTitle 的 title 属性作为 type 参数
        const titleType = '养老保险情况'
        const response = await getCsdnInterface1('rkhl_test', { type: titleType })

        console.log('接口返回数据:', response.data)

        // 处理接口返回的数据
        if (response.data && response.data.successful && response.data.data) {
          this.chartData = this.processChartData(response.data.data)
        } else {
          // 如果接口返回空数据或失败，使用空数组
          this.chartData = []
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        // 请求失败时使用空数组
        this.chartData = []
      } finally {
        this.loading = false
        this.initChart()
      }
    },

    processChartData(data) {
      // 根据实际接口返回的数据结构进行处理
      // 这里假设返回的是数组格式，需要根据实际情况调整
      if (Array.isArray(data) && data.length > 0) {
        return data.map((item, index) => ({
          name: item.name || item.category || `类别${index + 1}`,
          value: item.value || item.count || item.num || 0,
        }))
      }

      // 如果数据格式不符合预期，返回默认数据
      return [
        { name: '参保', value: 820 },
        { name: '未参保', value: 180 },
      ]
    },

    initChart() {
      const chart = this.$echarts.init(document.getElementById('pensionInsuranceStatus'))

      const categories = this.chartData.map((item) => item.name)
      const values = this.chartData.map((item) => item.value)

      const option = {
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#13C2C2',
          textStyle: { color: '#fff', fontSize: 26 },
          formatter: '{b}: {c}',
        },
        grid: { left: '10%', right: '10%', top: '14%', bottom: '12%' },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: { color: '#fff', fontSize: 24 },
          axisLine: { lineStyle: { color: '#333' } },
        },
        yAxis: {
          type: 'value',
          axisLabel: { color: '#fff', fontSize: 24 },
          axisLine: { lineStyle: { color: '#333' } },
          splitLine: { lineStyle: { color: '#333' } },
        },
        series: [
          {
            type: 'bar',
            data: values,
            itemStyle: {
              color: '#13C2C2',
              borderRadius: [4, 4, 0, 0],
            },
            barWidth: 24,
            animationDuration: 1000,
            animationEasing: 'cubicOut',
          },
        ],
      }
      chart.setOption(option)

      // 响应式处理
      window.addEventListener('resize', () => {
        chart.resize()
      })
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>

