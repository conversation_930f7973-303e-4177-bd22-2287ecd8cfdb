<!--
 * @Description:
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2022-11-07 11:17:21
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-25 09:31:25
-->
<template>
  <div class="wrapper" id="wrapper">
    <div class="TopTitle">
      <div class="tabs">
        <div class="tab" v-for="(item, i) in currentLeftTabs" :key="i" @click="handleTabClick(item)">
          <div class="tabName" :class="isTabActive(item) ? 'active_name' : ''">{{ item.name }}</div>
        </div>
      </div>
      <div class="mainTitle" @click="titleClick">
        <div class="title">{{ titleName }}</div>
      </div>
      <div class="tabs">
        <div
          class="tab"
          :class="{ 'has-dropdown': item.name === '强补固拓' && currentModule === 'industry' }"
          v-for="(item, i) in currentRightTabs"
          :key="i"
          @click="handleTabClick(item)"
          @mouseenter="item.name === '强补固拓' && currentModule === 'industry' && showDropdown()"
          @mouseleave="item.name === '强补固拓' && currentModule === 'industry' && hideDropdown()"
        >
          <div class="tabName" :class="isTabActive(item) ? 'active_name' : ''">{{ item.name }}</div>
          <div
            v-if="item.name === '强补固拓' && showCylDropdown && currentModule === 'industry'"
            class="cyl-dropdown"
            @click.stop
          >
            <div
              class="cyl-dropdown-item"
              :class="{ selected: selectedCylId === opt.id }"
              v-for="opt in cylOptions"
              :key="opt.id"
              @click.stop="selectCyl(opt.id)"
            >
              {{ opt.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <router-view></router-view>
  </div>
</template>

<script>
import { getCsdnInterface } from '@/api/csdnIndexApi'

export default {
  components: {},
  data() {
    return {
      titleName: '工业智服',
      currentModule: 'industry', // 'industry' 或 'population'
      tab1: [
        { name: '产业智服', router: '/qyzf' },
        { name: '强补固拓', router: '/qyzfCyl' },
      ],
      tab2: [
        { name: '实时人口情况', router: '/rkhl' },
        {
          name: '风险预警分析',
          router: '/fxyjfx',
        },
      ],
      showCylDropdown: false,
      selectedCylId: null, // 当前选中的产业链ID
      cylOptions: [
        { id: 210, name: '食品加工产业链' },
        { id: 211, name: '金属制造产业链' },
        { id: 212, name: '光电子信息产业链' },
      ],
      time: '',
      date: '',
      timer: '',
      weather: {
        weatherIcon: '',
        temp: '',
        wind: '',
      },
      dialogShow: false,
      hideMapRoutes: ['/IndicatorCenter'],
    }
  },
  mounted() {
    this.updateSelectedCyl()
    this.updateTitleByRoute()
  },
  computed: {
    currentLeftTabs() {
      return this.currentModule === 'industry' ? [this.tab1[0]] : [this.tab2[0]]
    },
    currentRightTabs() {
      return this.currentModule === 'industry' ? [this.tab1[1]] : [this.tab2[1]]
    },
  },
  watch: {
    $route() {
      this.updateSelectedCyl()
      this.updateTitleByRoute()
    },
  },
  methods: {
    titleClick() {
      if (this.currentModule === 'industry') {
        // 切换到人口活力模块
        this.currentModule = 'population'
        this.titleName = '人口活力'
        this.$router.push('/rkhl') // 默认加载实时人口情况
      } else {
        // 切换回工业智服模块
        this.currentModule = 'industry'
        this.titleName = '工业智服'
        this.$router.push('/qyzf') // 默认加载产业智服
      }
    },
    pageJump(r) {
      this.$router.push(r)
    },
    isTabActive(item) {
      // 根据当前路由判断标签是否激活
      const currentPath = this.$route.path
      if (item.name === '产业智服' && currentPath === '/qyzf') {
        return true
      }
      if (item.name === '强补固拓' && (currentPath === '/qyzfCyl' || currentPath === '/dyCyl')) {
        return true
      }
      if (item.name === '实时人口情况' && currentPath === '/rkhl') {
        return true
      }
      if (item.name === '风险预警分析' && currentPath === '/fxyjfx') {
        return true
      }
      return false
    },
    handleTabClick(item) {
      if (item.name === '强补固拓') {
        // 如果当前不在 /dyCyl 页面，则跳转到新能源汽车
        this.$router.push({ path: '/qyzfCyl' })
      } else {
        this.pageJump(item.router)
      }
    },
    showDropdown() {
      this.showCylDropdown = true
    },
    hideDropdown() {
      this.showCylDropdown = false
    },
    selectCyl(id) {
      this.showCylDropdown = false
      this.titleName = '强补固拓'
      this.$router.push({ path: '/dyCyl', query: { id: String(id) } })
    },
    updateSelectedCyl() {
      // 如果当前在 dyCyl 页面，根据路由参数设置选中状态
      if (this.$route.path === '/dyCyl' && this.$route.query.id) {
        this.selectedCylId = parseInt(this.$route.query.id)
      } else {
        this.selectedCylId = null
      }
    },
    updateTitleByRoute() {
      const path = this.$route.path
      if (path === '/qyzfCyl' || path === '/dyCyl' || path.startsWith('/qyzf')) {
        this.currentModule = 'industry'
        this.titleName = '工业智服'
      } else if (path === '/rkhl' || path === '/fxyjfx') {
        this.currentModule = 'population'
        this.titleName = '人口活力'
      }
    },
  },
}
</script>

<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100%;
  background-size: cover;
  .TopTitle {
    width: 7680px;
    height: 240px;
    background: url('../../assets/common/topbackground.png') no-repeat;
    background-size: cover;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 999;
    .tabs {
      display: flex;
      justify-content: center;
      align-items: center;
      .tab {
        cursor: pointer;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        .icon {
          width: 58px;
          height: 55px;
          background-size: cover;
        }
        .tabName {
          white-space: nowrap;
          font-weight: 700;
          font-size: 64px;
          color: #fff;
          margin-left: 17px;
        }
        .active_name {
          background: linear-gradient(90deg, #ffffff 0%, #b9ccff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      .has-dropdown {
        position: relative;
        padding-bottom: 10px;
      }
      .cyl-dropdown {
        position: absolute;
        top: 90px;
        left: 0;
        background: rgba(0, 0, 0, 0.7);
        border: 1px solid #5594c9;
        padding: 8px 0;
        min-width: 420px;
        z-index: 1000;
      }
      .cyl-dropdown-item {
        padding: 12px 20px;
        font-size: 36px;
        color: #fff;
        white-space: nowrap;
        cursor: pointer;
      }
      .cyl-dropdown-item + .cyl-dropdown-item {
        border-top: 1px solid rgba(255, 255, 255, 0.12);
      }
      .cyl-dropdown-item:hover {
        color: #c2e5ff;
      }
      .cyl-dropdown-item.selected {
        color: #5594c9;
        font-weight: bold;
      }
      .cyl-dropdown-item.selected:hover {
        color: #c2e5ff;
      }
    }
    .mainTitle {
      cursor: pointer;
      width: 1694px;
      height: 154px;
      background-size: cover;
      text-align: center;
      position: absolute;
      left: 2993px;
      .title {
        margin-top: 40px;
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 130px;
        color: #ffffff;
        line-height: 74px;
      }
    }
  }
}
</style>
