<template>
  <div class="chart-item">
    <div class="clickable-title" @click="handleTitleClick">
      <SubTitle title="人员流入流出情况" />
    </div>
    <div class="chart-container">
      <div id="populationFlow" class="chart"></div>
    </div>
  </div>
</template>

<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import SubTitle from '@/components/SubTitle.vue'

export default {
  name: 'PopulationFlow',
  components: {
    SubTitle,
  },
  mounted() {
    this.fetchFlowData()
  },
  methods: {
    // 处理标题点击事件
    handleTitleClick() {
      this.$emit('open-slsw', { tabIndex: 0 })
    },

    // 获取人员流入流出数据
    async fetchFlowData() {
      try {
        // 使用与FertilityStatus相同的接口调用方式
        const titleType = '人员流入流出情况'
        const response = await getCsdnInterface1('rkhl_test', { type: titleType })

        console.log('人员流入流出情况接口返回数据:', response.data)

        // 处理接口返回的数据
        if (response.data && response.data.successful && response.data.data) {
          const processedData = this.processFlowData(response.data.data)
          this.getLineCharts('populationFlow', processedData.xdata, processedData.inData, processedData.outData)
        } else {
          // 如果接口返回空数据或失败，使用默认数据
          this.initPopulationFlow()
        }
      } catch (error) {
        console.error('获取人员流入流出情况数据失败:', error)
        // 请求失败时使用默认数据
        this.initPopulationFlow()
      }
    },

    // 处理流入流出数据
    processFlowData(data) {
      if (Array.isArray(data) && data.length > 0) {
        const xdata = data.map(item => item.time || item.date || item.sort_time?.slice(5) || '未知')
        const inData = data.map(item => ((item.in_count || item.inflow || 0) / 10000).toFixed(2))
        const outData = data.map(item => ((item.out_count || item.outflow || 0) / 10000).toFixed(2))

        return { xdata, inData, outData }
      }

      // 默认数据
      return {
        xdata: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07'],
        inData: ['2.5', '3.2', '2.8', '3.5', '4.1', '3.8', '3.3'],
        outData: ['2.1', '2.8', '2.4', '3.1', '3.7', '3.4', '2.9']
      }
    },

    // 人员流入流出情况（备用方法）
    initPopulationFlow() {
      // 参考 changSan 方法中 name 为 '日' 的默认值实现
      getCsdnInterface1('csrk_lrlcqsdb', { day: 7 }).then((res) => {
        let xdata = [],
          datanow = [],
          data7 = []
          console.log('res.data.data',res);
        res.data.data.map((ele) => {
          xdata.push(ele.sort_time.slice(5))
          datanow.push((ele.in_count / 10000).toFixed(2))
          data7.push((ele.out_count / 10000).toFixed(2))
        })
        this.getLineCharts('populationFlow', xdata, datanow, data7)
      }).catch(() => {
        // 如果备用接口也失败，使用默认数据
        const defaultData = this.processFlowData([])
        this.getLineCharts('populationFlow', defaultData.xdata, defaultData.inData, defaultData.outData)
      })
    },

    // 人员流入流出对比图表绘制
    getLineCharts(dom, xdata, datanow, data7) {
      let myEc = this.$echarts.init(document.getElementById(dom))
      const option = {
        grid: {
          left: '5%',
          right: '6%',
          top: '20%',
          bottom: '1%',
          containLabel: true,
        },
        legend: {
          data: ['流入', '流出'],
          top: '5%',
          icon: 'circle',
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 30,
          textStyle: {
            color: '#fff',
            fontSize: 24,
            fontFamily: 'SourceHanSansCN-Medium',
          },
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          textStyle: {
            fontSize: 24,
            color: '#fff',
          },
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
        },
        xAxis: [
          {
            type: 'category',
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 9,
              textStyle: {
                color: '#fff',
                fontSize: 24,
              },
            },
            data: xdata,
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位：万人',
            min: function (value) {
              return (value.min - 5).toFixed(0)
            },
            nameTextStyle: {
              color: '#fff',
              fontSize: 24,
              padding: [0, -30, 0, 0],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                color: '#fff',
                fontSize: 24,
                fontFamily: 'SourceHanSansCN-Medium',
              },
            },
          },
        ],
        series: [
          {
            name: '流入',
            type: 'line',
            symbolSize: 10,
            itemStyle: {
              color: '#00C0FF',
              borderColor: '#00C0FF',
              borderWidth: 1,
            },
            data: datanow,
          },
          {
            name: '流出',
            type: 'line',
            symbolSize: 10,
            itemStyle: {
              color: '#FFC460',
              borderColor: '#FFC460',
              borderWidth: 1,
            },
            data: data7,
          },
        ],
      }

      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .clickable-title {
    cursor: pointer;
    transition: opacity 0.3s ease;

    &:hover {
      opacity: 0.8;
    }
  }

  .chart-container {
    position: relative;

    .chart-controls {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      gap: 8px;
      z-index: 10;

      .control-btn {
        width: 120px;
        height: 40px;
        font-size: 24px;
        font-weight: 400;
        color: #bfbcbc;
        background: rgba(0, 74, 166, 0.3);
        border: 2px solid #215293;
        text-align: center;
        line-height: 40px;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          color: #ffffff;
          font-weight: 700;
          background: rgba(0, 74, 166, 0.8);
        }

        &:hover {
          background: rgba(0, 74, 166, 0.6);
          color: #ffffff;
        }
      }
    }

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>
