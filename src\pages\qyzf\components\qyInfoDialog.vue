<template>
  <el-dialog
    :top="winTop"
    ref="commomDialog"
    custom-class="custom-class-dialog"
    :show-close="false"
    :modal="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="visible"
    :modal-append-to-body="true"
    :append-to-body="false"
  >
    <div class="qyinfo-wrap">
      <div class="dialog-top">
        <span>企业详情</span>
        <div class="flex-c">
          <span class="reset-box" @click="resetClick">重置搜索</span>
          <i class="el-icon-close" style="float: right; cursor: pointer" @click="close"></i>
        </div>
      </div>
      <div class="qyinfo-dialog-con">
        <!-- 筛选区域 -->
        <div class="qyinfo-select-box">
          <div class="search-con">
            <div class="qyinfo-search-input">
              <el-input
                placeholder="找企业"
                v-model="keyword"
                class="input-with-select"
                @keyup.enter.native="changeData"
                @input="inputFun"
              ></el-input>
              <div
                class="search-value-list"
                v-show="showSearch"
                v-loading="searchLoading"
                element-loading-text="拼命加载中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.8)"
              >
                <ul class="search-ul">
                  <li v-for="(i, index) in restaurants" :key="index" @click="handleSelect(i)">{{ i.value }}</li>
                  <li v-if="restaurants.length == 0 && !searchLoading" class="no-data">暂无数据</li>
                </ul>
              </div>
            </div>
            <div class="search-btn el-icon-search" @click="openDialog"></div>
          </div>
          <div class="select-item" v-for="(ele, i) in selectData" :key="i">
            <div class="select-name">{{ ele.name }} :</div>
            <div v-if="ele.name != '更多筛选'" :class="['select-list', ele.flag ? '' : 'remove-more']">
              <ul class="ul-list">
                <li
                  v-for="(item, index) in ele.children"
                  :key="index"
                  @click="clickItem(ele, item)"
                  :class="[ele.value == item ? 'click-color' : '']"
                >
                  <span v-if="ele.name == '县市区' && item == '金华市'">市本级</span>
                  <span v-else>{{ item }}</span>
                </li>
              </ul>
            </div>
            <div v-else class="select-list change-box">
              <div :class="['box1', form.qylx != '' ? 'click-color2' : '']">
                <el-select @change="hadnleChange('qylx')" v-model="form.qylx" placeholder="企业类型">
                  <el-option
                    v-for="item in qylxData"
                    :key="item.name"
                    :label="item.name"
                    :value="item.name"
                  ></el-option>
                </el-select>
              </div>
              <div :class="['box1', form.fxlx != '' ? 'click-color2' : '']">
                <el-cascader
                  :options="fxlxData"
                  v-model="form.fxlx"
                  :props="{ multiple: true, checkStrictly: true }"
                  collapse-tags
                  @change="hadnleChange('fxlx')"
                  placeholder="风险类型"
                  popper-class="dropDownPanel"
                ></el-cascader>
              </div>
              <div :class="['box1', form.djzt != '' ? 'click-color2' : '']">
                <el-select @change="hadnleChange('djzt')" v-model="form.djzt" placeholder="登记状态">
                  <el-option
                    v-for="item in djztData"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </div>
              <div :class="['box1', form.cbrs != '' ? 'click-color2' : '']">
                <el-cascader
                  v-model="form.cbrs"
                  :options="cbrsData"
                  :props="{ multiple: true, checkStrictly: true }"
                  collapse-tags
                  @change="hadnleChange('cbrs')"
                  placeholder="参保人数"
                  popper-class="dropDownPanel"
                ></el-cascader>
              </div>
              <div :class="['box1', form.qyzf ? 'click-color' : '']" style="cursor: pointer">
                <div @click="hadnleChange('qyjkpx')">健康评级</div>
              </div>
            </div>
            <div v-if="(i < 3 && i != 0) || i == 4" class="show-more" @click="showMoreFun(ele)">
              {{ ele.flag ? '收回' : '更多' }}
            </div>
          </div>
        </div>
        <!-- 数据区域 -->
        <div class="select-data-box">
          <!-- 相关企业 -->
          <div class="data-sum">
            相关企业
            <span class="s-c-yellow-gradient">{{ sum }}</span>
            家
            <span style="float: right" @click="openQyfx">企业分析</span>
          </div>
          <!-- 企业列表 -->
          <div class="data-list">
            <div
              class="data-item"
              style="cursor: pointer"
              v-for="(item, i) in qyData"
              :key="i"
              @click="openXqIframe(item)"
            >
              <div style="width: 100%; display: flex; justify-content: space-between; align-items: center">
                <p>
                  <span class="qymc">{{ item.qymc }}</span>
                  <span class="state-css">{{ item.djzt }}</span>
                </p>
                <div
                  style="margin-right: 50px; font-size: 38px"
                  :class="
                    parseFloat(item.qyzf) < 40
                      ? 'text_red'
                      : parseFloat(item.qyzf) > 50
                      ? 'text_green'
                      : item.qyzf
                      ? 'text_yellow'
                      : ''
                  "
                >
                  {{ item.qyzf || '-' }}
                </div>
              </div>
              <ul class="data-ul move-left">
                <li style="display: flex; margin-right: 40px; white-space: nowrap">
                  <div>法定代表人:</div>
                  <div v-if="item.fddbr">{{ item.fddbr }}</div>
                  <div v-else>暂无</div>
                </li>
                <li style="display: flex; margin-right: 40px; white-space: nowrap">
                  <div>注册资本:</div>
                  <div v-if="item.zczb">{{ item.zczb }}</div>
                  <div v-else>暂无</div>
                </li>
                <li style="display: flex; margin-right: 40px; white-space: nowrap">
                  <div>企业用工:</div>
                  <div v-if="item.cbrs">{{ item.cbrs }}</div>
                  <div v-else>暂无</div>
                </li>

                <li>经营场所: {{ item.xxdz }}</li>
              </ul>
              <ul class="data-ul">
                <li class="color-blue" v-for="(o, i) in item.gbhy" :title="o" :key="i">#{{ o }}</li>
              </ul>
            </div>
          </div>
          <!-- 分页 -->
          <div class="data-page">
            <el-pagination
              background
              layout="total, prev, pager, next, jumper"
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
              :total="total"
              :page-size="page"
            ></el-pagination>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import $ from 'jquery'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    qylx: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      winTop: '40px',
      total: 0,
      page: 1,
      sum: '',
      // 筛选的数据
      selectData: [
        {
          name: '县市区',
          flag: false,
          value: '开发区',
          strName: 'xsq',
          children: [
            '全部',
            // '金华市',
            // '婺城区',
            // '金东区',
            // '兰溪市',
            // '东阳市',
            // '义乌市',
            // '永康市',
            // '浦江县',
            // '武义县',
            // '磐安县',
            // '开发区',
          ],
        },
        {
          name: '国标行业',
          flag: false,
          value: '全部',
          strName: 'gbhy',
          children: [
            '全部',
            '农、林、牧、渔业',
            '采矿业',
            '制造业',
            '电力、热力、燃气及水生产和供应业',
            '建筑业',
            '批发和零售业',
            '交通运输、仓储和邮政业',
            '住宿和餐饮业',
            '信息传输、软件和信息技术服务业',
            '金融业',
            '房地产业',
            '租赁和商务服务业',
            '科学研究和技术服务业',
            '水利、环境和公共设施管理业',
            '居民服务、修理和其他服务业',
            '教育',
            '卫生和社会工作',
            '文化、体育和娱乐业',
            '公共管理、社会保障和社会组织',
            '国际组织',
          ],
        },
        {
          name: '注册资本',
          flag: false,
          value: '全部',
          strName: 'zczb',
          children: [
            '全部',
            '100万以内',
            '100万-200万',
            '200万-500万',
            '500万-1000万',
            '1000万-2000万',
            '2000万-3000万',
            '3000万-4000万',
            '4000万-5000万',
            '5000万以上',
          ],
        },
        {
          name: '成立日期',
          flag: false,
          value: '全部',
          strName: 'clrq',
          children: [
            '全部',
            '2024',
            '2023',
            '2022',
            '2021',
            '2019',
            '2018',
            '2017',
            '2016',
            '2015',
            '2014',
            '2013',
            '2012',
            '2011',
          ],
        },
        {
          name: '产业链',
          flag: false,
          value: '全部',
          strName: 'cyl_xx',
          children: [
            '全部',
            '五金',
            '纺织服装',
            '智能光伏及新型储能',
            '新能源汽车及关键零部件',
            '先进装备制造业',
            '生物医药与医疗机械',
            '量子计算',
            // '氢能',
            '汽摩配',
            '中药材',
            // '节能环保',
            '电子信息',
            '智能家居',
            '新材料',
            // '绿色食品',
            // '绿色建筑',
            '信创',
            // '通用航空',
            '电动（园林）工具',
            '磁性材料',
            '保温杯（壶）',
          ],
        },
        {
          name: '更多筛选',
          flag: false,
          value: '',
          strName: 'sx',
          children: ['企业类型', '风险类型', '登记状态', '参保人数'],
        },
      ],
      // 数据
      qyData: [],
      allData: [],
      // 企业类型
      qylxData: [
        { name: '上市企业' },
        { name: '规上企业' },
        { name: '小微企业' },
        { name: '独角兽企业' },
        { name: '专精特新小巨人企业' },
        { name: '专精特新中小企业' },
        { name: '科技小巨人研究中心' },
        { name: '科技企业孵化器' },
        { name: '技术创新示范企业' },
        { name: '众创空间' },
        { name: '隐形冠军企业' },
        { name: '技术先进型服务企业' },
        { name: '创新型中小企业' },
      ],
      // 风险类型
      fxlxData: [
        { label: '拖欠工资', value: 'tqgz' },
        { label: '严重违法', value: 'yzwf' },
        { label: '失信信息', value: 'sxxx' },
        { label: '行政处罚', value: 'xzcf' },
        { label: '环保处罚', value: 'hbcf' },
        { label: '经营异常', value: 'jyyc' },
        { label: '社保人数骤降', value: '社保人数骤降' },
        { label: '营收骤降', value: '营收骤降' },
        { label: '税收骤降', value: '税收骤降' },
        { label: '资产总计骤降', value: '资产总计骤降' },
        { label: '破产重整', value: '破产重整' },
        { label: '裁判文书', value: '裁判文书' },
        { label: '清算信息', value: '清算信息' },
        { label: '动产抵押', value: '动产抵押' },
        { label: '股权冻结', value: '股权冻结' },
        { label: '被执行人', value: '被执行人' },
        { label: '限制高消费', value: '限制高消费' },
      ],
      // 登记状态
      djztData: [
        { label: '变更', value: '变更' },
        { label: '吊销', value: '吊销' },
        { label: '备案', value: '备案' },
        { label: '开业', value: '开业' },
        { label: '歇业', value: '歇业' },
        { label: '注销', value: '注销' },
        { label: '迁出', value: '迁出' },
        { label: '其他', value: '其他' },
      ],
      // 参保人数
      cbrsData: [
        { label: '0人', value: '0人' },
        { label: '1-49人', value: '1-49人' },
        { label: '50-99人', value: '50-99人' },
        { label: '1000-9999人', value: '1000-9999人' },
        { label: '1000人以上', value: '1000人以上' },
      ],
      // 过滤条件
      form: {
        xsq: '全部',
        gbhy: '全部',
        zczb: '全部',
        clrq: '全部',
        qylx: '',
        fxlx: '',
        djzt: '开业',
        cbrs: '',
        tqgz: '',
        yzwf: '',
        sxxx: '',
        xzcf: '',
        hbcf: '',
        jyyc: '',
        cyl_xx: '全部',
        qyzf: '', //健康评级
      },
      keyName: '',
      //找企业
      keyword: '',
      clickKey: false,
      searchActive: null,
      restaurants: [],
      allMessage: [],
      showSearch: false,
      searchLoading: false,
      clickKey: false,
    }
  },
  watch: {
    if(newVal) {
      let that = this
      this.$nextTick(() => {
        setTimeout(() => {
          that.winTop =
            (document.documentElement.clientHeight - $(that.$refs.commomDialog.$el).find('.el-dialog').height()) / 2 +
            'px'
        }, 100)
      })
    },
  },
  mounted() {
    if (this.qylx) {
      this.form.qylx = this.qylx
    }
    this.$nextTick(() => {
      this.getKeyName()
    })
  },
  methods: {
    getKeyName() {
      let that = this
      getCsdnInterface1('qyhx_ent_comment_al').then((res) => {
        that.keyName = res.data.data[0].json_result
        that.getData(1)
      })
    },
    getData(pageNum) {
      let that = this
      let tsqy =
        this.form.qylx == '上市企业' ||
        this.form.qylx == '小微企业' ||
        this.form.qylx == '规上企业' ||
        this.form.qylx == ''
          ? ''
          : this.form.qylx
      let params = {
        gbhy: this.form.gbhy == '全部' ? '' : this.form.gbhy,
        zczb: this.form.zczb == '全部' ? '' : this.form.zczb,
        clrq: this.form.clrq == '全部' ? '' : this.form.clrq,
        djzt: this.form.djzt,
        cbrs: this.form.cbrs,
        ssqy: this.form.qylx == '上市企业' ? '1' : '',
        xwqy: this.form.qylx == '小微企业' ? '1' : '',
        gsqy: this.form.qylx == '规上企业' ? '1' : '',
        tsqy: tsqy,
        pagenum: pageNum,
        tqgz: this.form.tqgz,
        yzwf: this.form.yzwf,
        sxxx: this.form.sxxx,
        xzcf: this.form.xzcf,
        hbcf: this.form.hbcf,
        jyyc: this.form.jyyc,
        cyl_xx: this.form.cyl_xx == '全部' ? '' : this.form.cyl_xx,
        qyzf: this.form.qyzf, //健康评级
      }
      getCsdnInterface1('qyhx_qyxx_al', params).then((res) => {
        console.log(res)
        if (res.data.responsecode == 200) {
          let result = res.data.data[0]
          console.log(result)
          that.sum = Number(res.data.data[0].total).toLocaleString()
          let allData = result.result
          that.total = result.total
          that.page = result.pagesize
          this.qyData = allData.map((ele) => {
            ele.gbhy = []
            let qybq = ele.qybq[0]
            for (let o in qybq) {
              let bqxxKey = qybq[o]
              if (bqxxKey == 1 || bqxxKey == '1') {
                let bqxxValue = that.keyName[o]
                if (bqxxValue == '变更') continue
                ele.gbhy.push(bqxxValue)
              }
            }
            return ele
          })
        }
      })
    },
    resetClick() {
      this.selectData.forEach((item, i) => {
        if (i == 0) {
          item.value == '开发区'
        } else {
          item.value = '全部'
        }
      })
      this.form = {
        xsq: '开发区',
        gbhy: '全部',
        zczb: '全部',
        clrq: '全部',
        qylx: '',
        fxlx: '',
        djzt: '开业',
        cbrs: '',
        tqgz: '',
        yzwf: '',
        sxxx: '',
        xzcf: '',
        hbcf: '',
        jyyc: '',
        cyl_xx: '全部',
      }
      this.handleCurrentChange(1)
    },
    // 筛选
    clickItem(data, item) {
      let index = this.selectData.findIndex((o) => o.strName == data.strName)
      if (this.selectData[index].value == item) return
      this.selectData[index].value = item
      this.form[data.strName] = item
      this.handleCurrentChange(1)
    },
    // 多选
    hadnleChange(name) {
      if (name == 'fxlx') {
        let obj = {
          tqgz: '',
          yzwf: '',
          sxxx: '',
          xzcf: '',
          hbcf: '',
          jyyc: '',
        }
        for (let i in obj) {
          this.form[i] = ''
        }
        for (let j = 0; j < this.form.fxlx.length; j++) {
          let strName = this.form.fxlx[j]
          this.form[strName] = '1'
        }
      } else if (name == 'qyjkpx') {
        if (this.form.qyzf !== '') {
          this.form.qyzf = ''
        } else {
          this.form.qyzf = '1'
        }
        console.log(this.form)
      }
      this.handleCurrentChange(1)
    },
    // 每页多少数量点击
    handleSizeChange(val) {
      this.page = val
      this.handleCurrentChange(1)
    },
    //分页点击
    handleCurrentChange(e) {
      this.getData(e)
    },
    // 显示更多
    showMoreFun(ele) {
      let index = this.selectData.findIndex((o) => o.strName == ele.strName)
      this.selectData[index].flag = !this.selectData[index].flag
    },
    close() {
      this.$emit('close')
    },
    //找企业
    //@input 在 Input 值改变时触发
    changeData(value) {
      this.clickKey = false
      if (this.keyword != '') {
        this.restaurants = []
        this.allMessage = []
        this.showSearch = true
        this.handleChange()
      } else {
        this.keyword = ''
      }
      // this.changeHeight()
    },
    handleChange() {
      let that = this
      this.searchLoading = true
      let params = {
        name: this.keyword,
      }
      getCsdnInterface1('qyhx_qyxx_search_al', params).then((res) => {
        let arr = res.data.data
        that.allMessage = arr
        let restaurants = arr.map((item) => {
          return {
            value: item.qymc,
          }
        })
        that.restaurants = restaurants ? restaurants : []
        console.log(11111, restaurants)
        that.searchLoading = false
      })
    },
    // 修改高度
    changeHeight() {
      let parentIframe = parent.document.getElementById('serach-btn')
      console.log(parentIframe)
      if (this.keyword != '') {
        parentIframe.style.height = '520px'
      } else {
        parentIframe.style.height = '200px'
      }
    },
    inputFun() {
      this.allMessage = []
      this.clickKey = false
      if (this.keyword == '') {
        this.showSearch = false
        // this.changeHeight()
      }
    },
    //@select 点击选中建议项时触发
    handleSelect(item) {
      this.keyword = item.value
      this.clickKey = true
      this.openDialog()
    },
    openDialog() {
      this.restaurants = []
      let that = this
      if (this.keyword != '' && this.allMessage.length == 0) {
        this.searchLoading = true
        this.clickKey = false
        let params = {
          name: this.keyword,
        }
        getCsdnInterface1('qyhx_qyxx_search_al', params).then((res) => {
          this.searchLoading = false
          that.showSearch = false
          let arr = res.data.data ? res.data.data : []
          this.$emit('openDialog', arr)
        })
      } else if (this.keyword != '') {
        this.searchLoading = false
        this.showSearch = false
        if (this.clickKey) {
          let msg = this.allMessage.filter((o) => o.qymc == this.keyword)
          this.allMessage = msg
          this.clickKey = false
        }
        this.$emit('openDialog', this.allMessage)
      }
      this.keyword = ''
    },
    openXqIframe(item) {
      this.$emit('openDialog', [item])
    },
    openQyfx() {
      // window.parent.lay.openIframe({
      //   type: 'openIframe',
      //   name: 'qyhx-scztfx',
      //   src: baseURL.url + '/static/citybrain/qyhx/commont/qyhx-scztfx.html',
      //   left: '0',
      //   top: '0',
      //   width: '7680px',
      //   height: '2160px',
      //   zIndex: '505',
      //   argument: {
      //     iframeName: 'qyhx-scztfx',
      //     back: 'back',
      //   },
      // })
    },
  },
}
</script>

<style lang="less" scoped>
// 重置element-ui弹框
.el-dialog__wrapper {
  position: relative;
  .el-dialog {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
/deep/.el-dialog {
  width: 3000px;
  height: 1800px;
  // background: rgba(0, 15, 55, 0.9);
  background: transparent;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  // margin-left: 2350px !important;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}
.title-container .close-btn {
  float: right;
  margin: 15px;
  cursor: pointer;
  font-size: 36px;
  color: white;
}

body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 2160px;
  background-color: #00000065;
  display: flex;
  justify-content: center;
  align-items: center;
}

::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

::-webkit-scrollbar-thumb {
  border-radius: 6px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}

.qyinfo-wrap {
  width: 3000px;
  height: 1800px;
  padding: 90px 65px 80px;
  box-sizing: border-box;
  background: url('@/pages/qyzf/img/dialog-bg.png');
  background-size: 100% 100%;
  overflow: hidden;

  /* 头部 */
  .dialog-top {
    font-size: 50px;
    font-weight: bold;
    color: #ffffff;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .dialog-top > span:first-child {
    background: linear-gradient(0deg, #ffcc00 0.4150390625%, #ffffff 99.5849609375%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .reset-box {
    display: inline-block;
    font-size: 32px;
    letter-spacing: 2px;
    color: #fff !important;
    background: linear-gradient(180deg, #17aee0 0%, #0166a6 100%);
    padding: 0 20px;
    margin-right: 100px;
    cursor: pointer;
  }

  /* 内容 */
  .qyinfo-dialog-con {
    width: 100%;
    height: 1550px;
    padding: 20px;
    margin-top: 10px;
    box-sizing: border-box;
  }

  .qyinfo-select-box {
    width: 100%;
    height: 500px;
    border-bottom: 2px solid #00c0ff66;
    position: relative;
    overflow: hidden;
    overflow-y: auto;
    padding: 0 65px 20px 20px;
    box-sizing: border-box;
  }

  .qyinfo-select-box::before {
    content: '';
    width: 20px;
    height: 20px;
    /* background-image: url('/static/images/icon/icon1.png'); */
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    bottom: 0;
  }

  .select-data-box {
    width: 100%;
    height: 970px;
    margin-top: 30px;
  }

  /* 筛选区域的样式 */
  .select-item {
    width: 100%;
    margin-bottom: 20px;
    color: #fff;
    font-size: 32px;
    display: flex;
    justify-content: space-between;
  }

  .select-name {
    width: 200px;
    text-align: right;
    padding-right: 50px;
    box-sizing: border-box;
  }

  .select-list {
    flex: 1;
  }
  .select-list .el-input__inner {
    background: transparent !important;
    font-size: 32px !important;
  }

  .show-more {
    background: linear-gradient(180deg, #17aee0 0%, #0166a6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-left: 50px;
    cursor: pointer;
  }

  .click-color {
    background: linear-gradient(180deg, #17aee0 0%, #0166a6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .click-color2 .el-input__inner {
    color: #0166a6 !important;
  }

  .ul-list {
    display: flex;
    flex-wrap: wrap;
  }

  .ul-list > li {
    margin-right: 50px;
    margin-bottom: 10px;
    cursor: pointer;
    /* cursor: not-allowed; */
    /* 暂时无数据不支持点击 */
  }

  .ul-list > li:last-child {
    margin-right: 0;
  }

  /* 更多 */
  .remove-more {
    height: 50px;
    overflow: hidden;
  }

  /* 企业数据 */
  .data-sum {
    width: 100%;
    font-size: 28px;
    margin-bottom: 6px;
    color: #fff;
    font-weight: 400;
    padding-left: 20px;
    box-sizing: border-box;
  }

  .data-list {
    width: 100%;
    height: 800px;
    color: #fff;
    font-size: 32px;
    margin-top: 20px;
    overflow-y: auto;
  }

  ::v-deep .data-page {
    width: 100%;
    height: 100px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    /* 新的分页样式 */
    .el-pagination.is-background .btn-next,
    .el-pagination.is-background .btn-prev,
    .el-pagination.is-background .el-pager li {
      background: #5f7b96;
      color: #fff;
      font-size: 34px;
      font-weight: normal;
      height: 60px;
      line-height: 58px;
      box-sizing: border-box;
    }

    .el-pager li.active + li {
      border-left: 1px solid transparent !important;
    }

    .el-pagination.is-background .el-pager li:not(.disabled).active {
      background: #0166a6;
      border-radius: 3px 3px 3px 3px;
      height: 60px;
      line-height: 60px;
      box-sizing: border-box;
    }

    .el-pager li {
      background: #5f7b96;
      padding: 0 20px;
      border: 1px solid transparent;
      box-sizing: border-box;
    }

    .el-pagination .btn-next .el-icon,
    .el-pagination .btn-prev .el-icon {
      font-size: 30px;
    }

    .el-pagination .btn-next,
    .el-pagination .btn-prev {
      width: 60px;
    }

    /* 分页- */
    .el-pagination__total {
      color: #fff;
      font-size: 32px !important;
      margin: 0;
      padding-right: 20px;
    }

    /* 分页-多少页 */
    .el-pagination__sizes .el-input .el-input__inner {
      font-size: 32px;
      background-color: transparent;
      border-color: #6f788a;
      height: 60px !important;
      line-height: 60px;
      color: #fff;
    }

    .el-pagination .el-select .el-input .el-input__inner {
      padding-right: 51px;
    }

    .el-select .el-input .el-select__caret {
      font-size: 40px;
      color: #6f788a;
      transform: rotate(0);
      padding-top: 10px;
    }

    .el-select .el-input .el-select__caret.is-reverse {
      transform: rotate(180deg);
      margin-top: -10px;
    }

    .el-pagination .el-select .el-input {
      width: 250px;
      margin: 0;
    }

    .el-pagination button,
    .el-pagination span:not([class*='suffix']) {
      height: 60px;
      line-height: 60px;
    }

    .el-pagination .el-input__suffix {
      right: 15px;
    }

    .el-select-dropdown__item {
      font-size: 32px;
      background-color: transparent;
      color: #fff;
      border-color: #6f788a;
      margin-bottom: 10px;
    }

    .el-select-dropdown {
      background-color: #041330;
      border-color: #e4e7ed4f;
    }

    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
      background-color: transparent;
    }

    .el-popper[x-placement^='bottom'] .popper__arrow {
      display: none;
    }

    .el-pagination button,
    .el-pagination span:not([class*='suffix']) {
      font-size: 32px;
      color: #fff;
    }

    .el-pagination__editor.el-input .el-input__inner {
      height: 60px;
      font-size: 32px;
      background-color: transparent;
      color: #fff;
      border-color: #6f788a;
    }

    .el-pagination__editor.el-input {
      width: 100px;
      height: 60px;
      margin: 0 10px;
    }
  }

  /* 企业数据列表样式 */
  .data-item {
    width: 100%;
    margin-bottom: 30px;
    border-bottom: 2px solid #00c0ff66;
    padding: 0 0 20px 20px;
    box-sizing: border-box;
    overflow-y: auto;
  }

  .data-ul {
    display: flex;
  }

  .data-ul > li {
    margin-left: 50px;
  }

  .data-ul > li:first-child {
    margin-left: 0;
  }

  .move-left > li {
    margin: 10px 0 10px 0px;
  }

  .state-css {
    display: inline-block;
    padding: 0 10px;
    background: #0166a6;
    border-radius: 3px;
    font-size: 26px;
    margin-left: 15px;
  }

  .color-blue {
    color: #2f89ea;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  /* 更多筛选样式 */
  .change-box {
    display: flex;
  }

  ::v-deep .box1 {
    width: 280px;
    margin-right: 50px;
    .el-input__inner {
      height: auto;
      font-size: 32px;
      background-color: transparent;
      color: #fff;
      border-color: transparent !important;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      padding: 0 30px 0 0;
      text-align: center;
    }
    .el-select .el-input.is-focus .el-input__inner {
      border-color: transparent;
    }
    .el-select .el-input__inner:focus {
      border-color: transparent;
    }
    .el-input__inner:hover {
      border-color: transparent;
    }
    .el-input__icon:before {
      content: '\e78f';
      color: #fff;
    }
    .el-select .el-input {
      font-size: 40px;
    }

    .el-cascader .el-input .el-icon-arrow-down {
      font-size: 40px;
      padding-top: 10px;
    }
    .el-select .el-icon-arrow-up {
      font-size: 40px;
      padding-top: 10px;
    }
    .el-avatar,
    .el-cascader-panel,
    .el-radio,
    .el-radio--medium.is-bordered .el-radio__label,
    .el-radio__label {
      font-size: 32px;
    }
  }
  // .dropDownPanel {
  //   background: #041330 !important;
  //   background-color: #041330 !important;
  //   border: 1px solid #e4e7ed4f;
  //   font-size: 32px !important;
  // }
  // .el-cascader__dropdown .el-checkbox__inner {
  //   width: 25px;
  //   height: 25px;
  //   margin-right: 10px;
  // }

  // .el-cascader-node {
  //   margin-bottom: 10px;
  //   color: #fff;
  // }
  // .el-cascader-node:not(.is-disabled):focus,
  // .el-cascader-node:not(.is-disabled):hover {
  //   background: transparent;
  // }

  // ::v-deep .el-checkbox__inner::after {
  //   width: 10px;
  //   height: 20px;
  //   left: 7px;
  //   top: -3px;
  // }

  ::v-deep .el-cascader__tags .el-tag {
    background: transparent;
    margin: 0;
  }

  ::v-deep .el-tag.el-tag--info {
    background-color: transparent;
    color: #fff;
    height: 100% !important;
  }

  ::v-deep .el-tag {
    font-size: 30px;
    .el-icon-close {
      width: 32px;
      height: 32px;
      font-size: 32px;
      text-align: center;
      line-height: 32px;
    }
  }

  ::v-deep .el-cascader__tags .el-tag > span {
    height: 100%;
    line-height: 40px;
  }

  .search-con {
    width: 2500px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    margin: 0 auto;
    position: relative;
    margin-bottom: 40px;
  }

  .qyinfo-search-input {
    width: 100%;
    height: 85px !important;
    line-height: 85px;
    color: #fff;
    font-size: 36px;
    position: relative;
  }

  .search-btn {
    width: 50px;
    height: 50px;
    font-size: 42px;
    color: #7f97b2;
    text-align: center;
    line-height: 85px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 30px;
  }

  .search-value-list {
    width: 100%;
    position: absolute;
    top: 100px;
    overflow: hidden;
    z-index: 20;
  }

  .search-ul {
    width: 100%;
    height: 400px;
    overflow: hidden;
    overflow-y: auto;
    font-size: 32px;
    border: 1px solid #0d2c58 !important;
    background-color: #0d3451 !important;
    padding: 0 80px;
    box-sizing: border-box;
    margin: 0;
  }

  .search-ul > li {
    width: 100%;
    line-height: 80px;
    padding: 0 10px;
    border-bottom: 1px solid #134a75ba;
    box-sizing: border-box;
  }

  ::v-deep .qyinfo-search-input .el-input__inner {
    height: 85px !important;
    line-height: 85px;
    background-color: #1c385d94;
    font-size: 36px;
    width: 100%;
    color: #fff;
    border: 1px solid #1c385d94 !important;
    border-radius: 40px !important;
    padding: 0 80px 0 50px;
  }
  .el-input .el-input__inner {
    font-size: 36px !important;
    height: 85px;
  }

  .el-input__inner:hover {
    border: 1px solid #1c385d94;
  }

  .el-autocomplete {
    width: 100%;
  }

  .el-scrollbar,
  .el-autocomplete-suggestion {
    border: 1px solid #0d2c58 !important;
    background-color: #0d3451 !important;
  }

  .el-autocomplete-suggestion li:hover,
  .el-autocomplete-suggestion li.highlighted {
    background-color: #0000003b !important;
  }

  .el-autocomplete-suggestion li {
    color: #fff !important;
    font-size: 30px !important;
    line-height: 70px !important;
  }

  .el-loading-spinner i {
    font-size: 40px;
  }

  .el-loading-spinner .el-loading-text {
    font-size: 40px;
  }

  .el-loading-mask {
    top: -100px;
  }

  .flex-c {
    display: flex;
    align-items: center;
  }

  .reset-box {
    margin-left: 2300px;
  }

  .qyinfo-select-box {
    height: 560px;
  }

  .search-con {
    width: 2700px;
  }

  .select-data-box {
    height: 950px;
  }

  .data-list {
    height: 780px;
  }

  .data-item .qymc {
    font-size: 40px;
  }

  .text_red {
    color: red;
  }

  .text_yellow {
    color: orange;
  }

  .text_green {
    color: green;
  }
}
</style>
<style lang="less">
.dropDownPanel {
  background: #041330 !important;
  background-color: #041330 !important;
  border: 1px solid #e4e7ed4f;
  font-size: 32px !important;
  padding: 20px 10px;
  .el-cascader-node__label {
    color: #fff;
    font-size: 30px;
  }
  .el-cascader-node:hover,
  .el-cascader-node.hover {
    background-color: transparent;
  }
  .el-cascader__dropdown .el-checkbox__inner {
    width: 25px;
    height: 25px;
    margin-right: 10px;
  }

  .el-cascader-node {
    margin-bottom: 10px;
    color: #fff;
  }
  .el-cascader-node:not(.is-disabled):focus,
  .el-cascader-node:not(.is-disabled):hover {
    background: transparent;
  }

  .el-checkbox__inner::after {
    width: 10px;
    height: 20px;
    left: 7px;
    top: -3px;
  }
}
.el-select-dropdown {
  background-color: #041330;
  border-color: #e4e7ed4f;
}
.el-select-dropdown__item {
  font-size: 30px !important;
  line-height: 40px;
  background-color: transparent;
  color: #fff;
  border-color: #6f788a;
  margin-bottom: 10px;
}
.el-select-dropdown__item:hover,
.el-select-dropdown__item.hover {
  background-color: transparent;
}
</style>