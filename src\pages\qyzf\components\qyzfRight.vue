<template>
  <div class="qyzf-right">
    <MainTitle title="新质生产力" size="large" />
    <div class="xzscl-con">
      <div class="qyzfright-con-item">
        <SubTitle title="创新企业" />
        <div id="chart01" style="width: 100%; height: 550px"></div>
      </div>
      <div class="qyzfright-con-item">
        <SubTitle title="高素养人才" />
        <div class="s-flex">
          <div id="chart02" style="width: 324px; height: 550px; z-index: 10"></div>
          <div class="chart-detail" v-if="chartData2 && chartData2.length > 0">
            <div class="detail-top">{{ chartData2[0].name }}</div>
            <div class="detail-con">
              <span style="color: #9bd2ff">总人数：</span>
              <span>{{ chartData2[0].value }}人</span>
            </div>
            <div class="detail-top mt_1">{{ chartData2[1].name }}</div>
            <div class="detail-con mb_1">
              <span style="color: #9bd2ff">总人数：</span>
              <span>{{ chartData2[1].value }}人</span>
            </div>
            <div class="detail-top">{{ chartData2[2].name }}</div>
            <div class="detail-con">
              <span style="color: #9bd2ff">总人数：</span>
              <span>{{ chartData2[2].value }}人</span>
            </div>
          </div>
        </div>
        <img class="qyzfright-pie_bg" src="@/pages/qyzf/img/pie_bg.png" alt="" width="200" />
      </div>
      <div class="qyzfright-con-item">
        <SubTitle title="专利趋势图" />
        <div id="zhuanliqushi" style="width: 100%; height: 550px"></div>
      </div>
    </div>
    <div style="margin-top: 50px">
      <MainTitle title="政策直达" size="large" />
      <div class="con">
        <div class="qyzfright-con-item">
          <SubTitle :title="title1[0][dfIndex1]" />
          <ul class="title-item" style="cursor: pointer; justify-content: right; margin: 45px 0; height: 75px">
            <li
              v-for="(item, index) in dfTitleList"
              :class="[dfIndex1 == index ? 'title-item-active' : '']"
              @click="dfChange(index)"
            >
              {{ item }}
            </li>
          </ul>
          <div id="chart041" style="width: 650px; height: 600px"></div>
        </div>
        <div class="qyzfright-con-item">
          <SubTitle :title="title2[0]" />
          <div id="cyjj-chart" style="height: '750px'"></div>
        </div>
        <div class="qyzfright-con-item">
          <SubTitle :title="title3[0]" />
          <div class="table table1">
            <img
              src="@/pages/qyzf/img/back.png"
              alt=""
              @click="dfIndex3 = 0"
              style="cursor: pointer; position: absolute; top: 30px; right: 200px"
              v-show="dfIndex3 == 1"
            />
            <div class="th" v-show="dfIndex3 == 1">
              <div class="th_td" v-for="item in bottomTh" style="flex: 0.33">{{ item }}</div>
            </div>
            <div v-show="dfIndex3 == 0" id="chart061" style="width: 650px; height: 760px"></div>
            <div v-show="dfIndex3 == 1" class="tbody" id="box1">
              <div class="tr" v-for="(item, index) in cashData" :key="index" style="cursor: pointer">
                <div class="tr_td" style="flex: 0.33" :title="item.qymc">{{ item.qymc }}</div>
                <div class="tr_td" style="flex: 0.25" :title="item.ssqx">{{ item.ssqx }}</div>
                <div class="tr_td" style="flex: 0.35" :title="item.sqdfsj">{{ item.sqdfsj }}</div>
                <div class="tr_td" style="flex: 0.25" :title="item.ydfje">
                  {{ item.ydfje ? Math.round(item.ydfje) : '-' }}万
                </div>
              </div>
            </div>
            <div v-show="dfIndex3 == 1" style="float: right">
              <el-pagination
                background
                layout="total, prev, pager, next"
                :current-page.sync="page2"
                @current-change="handleCurrentChange2"
                :total="total2"
                :pager-count="5"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import MainTitle from '@/components/MainTitle.vue'
import SubTitle from '@/components/SubTitle.vue'

export default {
  name: 'UrbanSignsRight',
  components: { MainTitle, SubTitle },
  data() {
    return {
      currentYear: '2024',
      total1: 0,
      page1: 1,
      total2: 0,
      page2: 1,
      time: null,
      dom: null,
      chartData1: [
        {
          name: '国家级专精特新小巨人企业',
          value: 3,
        },
        {
          name: '省级专精特新企业',
          value: 40,
        },
        {
          name: '省级单项冠军企业',
          value: 8,
        },
        {
          name: '高新技术企业总数',
          value: 105,
        },
      ],
      chartData2: [
        // {
        //   name: '高水平创新型人才',
        //   value: 2500,
        // },
        // {
        //   name: '企业家',
        //   value: 1345,
        // },
        // {
        //   name: '高素养劳动者',
        //   value: 924419,
        // },
      ],
      chartData3: [
        {
          name: '2021',
          value1: 20,
          value2: 30,
        },
        {
          name: '2022',
          value1: 40,
          value2: 30,
        },
        {
          name: '2023',
          value1: 60,
          value2: 30,
        },
        {
          name: '2024',
          value1: 50,
          value2: 30,
        },
      ],
      bottomTh: ['企业名称', '所属地区', '申请兑付时间', '兑付金额'],
      title1: [['近五年兑付企业数变化趋势', '近五年兑付金额变化趋势']],
      title2: ['政策兑付企业数分布情况'],
      title3: ['实时兑付'],
      cashData: [],
      cityName: '',
      dfTitleList: ['兑付企业数', '兑付金额'],
      dfIndex1: 0,
      dfIndex3: 0,
    }
  },
  mounted() {
    this.initFun()
    this.initApi()
    this.getData1()
    this.cityChange('')
    this.initChart1()
    this.initChart2()
  },
  methods: {
    getData1() {
      getCsdnInterface1('qyhx_gszrc_al').then((res) => {
        this.chartData2 = res.data.data
        this.getChart2('chart02', this.chartData2)
      })
    },
    handleCurrentChange2(val) {
      this.page2 = val
      this.getCashList()
    },
    dfChange(index) {
      this.dfIndex1 = index
      this.initChart1()
    },
    initChart2() {
      getCsdnInterface1('qyhx_kfq_dfqys').then((res) => {
        let data = res.data.data.map((item) => {
          return {
            name: item.ywwd1,
            value: item.tjz,
          }
        })
        this.barLineCharts1('cyjj-chart', data, '条')
      })
    },
    initChart1() {
      if (this.dfIndex1 == 0) {
        getCsdnInterface1('qyhx_kfq_jwndfqs').then((res) => {
          let data = res.data.data.map((item) => {
            return {
              name: item.nf,
              value1: item.dfs,
              value2: item.dfzzl,
            }
          })
          // console.log('这是数据1', data)
          this.getChart4('chart041', data, '家', '数量')
        })
      } else {
        getCsdnInterface1('qyhx_kfq_jwndfqs').then((res) => {
          let data = res.data.data.map((item) => {
            return {
              name: item.nf,
              value1: item.ndfje,
              value2: item.dfjebhl,
            }
          })
          // console.log('这是数据2', data)
          this.getChart4('chart041', data, '万元', '金额')
        })
      }
    },
    getChart4(id, chartData, unit, name) {
      const that = this
      let myChart = echarts.init(document.getElementById(id))
      let xdata = chartData.map((item) => {
        return item.name
      })
      let ydata1 = chartData.map((item) => {
        return item.value1
      })
      let ydata2 = chartData.map((item) => {
        return item.value2
      })
      let option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          axisPointer: {
            type: 'shadow',
          },
          textStyle: {
            color: 'white',
            fontSize: '32',
          },
        },
        graphic: {
          type: 'text',
          left: 'center',
          top: 'middle',
          silent: true,
          invisible: chartData.length,
          style: {
            fill: 'white',
            text: '暂无数据',
            font: '32px system-ui',
          },
        },
        grid: {
          top: '15%',
          right: '15%',
          left: '20%',
          bottom: '15%',
        },
        xAxis: [
          {
            type: 'category',
            data: xdata,
            axisLine: {
              lineStyle: {
                color: 'rgba(122, 163, 197, 1)',
              },
            },
            axisLabel: {
              margin: 20,
              color: '#fff',
              textStyle: {
                fontSize: 28,
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            name: unit,
            nameTextStyle: {
              color: '#fff',
              fontSize: 28,
              padding: [0, 0, 20, 0],
            },
            axisLabel: {
              formatter: '{value}',
              color: '#fff',
              textStyle: {
                fontSize: 28,
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#FFFFFF',
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(122, 163, 197, 0)',
              },
            },
          },
          {
            show: true,
            name: '%',
            type: 'value',
            nameTextStyle: {
              fontSize: 28,
              color: '#fff',
              padding: [5, 0, 0, 0],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#fff',
              },
            },
          },
        ],
        series: [
          {
            name: name,
            type: 'bar',
            data: ydata1,
            barWidth: '20px',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(0, 177, 255, 1)', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'rgba(0,77,167,0)', // 100% 处的颜色
                    },
                  ],
                  false
                ),
                shadowColor: 'rgba(0,160,221,1)',
                shadowBlur: 4,
              },
            },
            label: {
              normal: {
                show: false,
              },
            },
          },
          {
            name: '增速',
            type: 'line',
            // symbolSize: "0",
            // smooth: true,
            itemStyle: {
              color: '#FF7D00',
            },
            lineStyle: {
              color: '#FF7D00',
            },
            yAxisIndex: 1,
            data: ydata2,
          },
        ],
      }
      myChart.setOption(option, true)
      myChart.off('click')
    },
    cityChange(name) {
      this.cityName = name
      // this.getCashList()
      this.initpart3()
    },
    initApi() {
      let that = this
      getCsdnInterface1('qyhx_sy_zlqst').then((res) => {
        let data = res.data.data.map((item) => {
          return {
            name: item.nf,
            value: item.xzs,
            value1: item.zs,
          }
        })
        that.getChart3('zhuanliqushi', data.reverse())
      })
    },
    barLineCharts1(id, data, unit) {
      const that = this
      echarts.init(document.getElementById(id)).dispose()
      let myChart = echarts.init(document.getElementById(id))
      const getArrByKey = (data, k) => {
        let key = k || 'value'
        let res = []
        if (data) {
          data.forEach(function (t) {
            res.push(t[key])
          })
        }
        return res
      }
      const getSymbolData = (data) => {
        let arr = []
        for (var i = 0; i < data.length; i++) {
          arr.push({
            value: data[i].value,
            symbolPosition: 'end',
          })
        }
        return arr
      }
      // console.log(getSymbolData(data));
      let option = {
        grid: {
          top: '6%',
          bottom: '6%',
          right: 20,
          left: '7%',
          containLabel: true,
        },
        graphic: {
          type: 'text',
          left: 'center',
          top: 'middle',
          silent: true,
          invisible: data.length,
          style: {
            fill: 'white',
            text: '暂无数据',
            font: '32px system-ui',
          },
        },
        xAxis: {
          show: false,
        },
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          axisPointer: {
            type: 'shadow',
          },
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
          formatter: (params) => {
            return params[0].name + ' : ' + echarts.format.addCommas(params[0].value) + unit
          },
        },
        yAxis: [
          {
            triggerEvent: true,
            show: true,
            inverse: true,
            data: getArrByKey(data, 'name'),
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },

            axisLabel: {
              show: true,
              interval: 0,
              color: '#fff',
              align: 'left',
              margin: 60,
              formatter: function (value, index) {
                return '{title|' + value + '}'
              },
              rich: {
                title: {
                  width: 50,
                  align: 'right',
                  fontSize: 36,
                },
              },
            },
          },
          {
            triggerEvent: true,
            show: true,
            inverse: true,
            data: getArrByKey(data, 'name'),
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              shadowOffsetX: '-20px',
              color: ['#fff'],
              align: 'left',
              verticalAlign: 'center',
              lineHeight: 40,
              fontSize: 36,
              formatter: function (value, index) {
                return data[index].value
              },
            },
          },
        ],
        series: [
          {
            name: 'XXX',
            type: 'pictorialBar',
            symbol: 'none',
            symbolSize: 10,
            symbolOffset: [5, 0],
            z: 12,
            itemStyle: {
              normal: {
                color: '#fff',
              },
            },
            data: getSymbolData(data),
            tooltip: {
              show: false,
            },
          },
          {
            name: '政策数',
            type: 'bar',
            showBackground: true,
            yAxisIndex: 0,
            data: data,
            barWidth: 20,
            // align: left,
            itemStyle: {
              normal: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#1677FF00', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#1677FF', // 100% 处的颜色
                    },
                  ],
                },
              },
            },
          },
        ],
      }

      myChart.setOption(option, true)
      tools.loopShowTooltip(myChart, option, { loopSeries: true })
    },
    barLineCharts2(id, data) {
      let myChart = echarts.init(document.getElementById(id))
      const getArrByKey = (data, k) => {
        let key = k || 'value'
        let res = []
        if (data) {
          data.forEach(function (t) {
            res.push(t[key])
          })
        }
        return res
      }
      const getSymbolData = (data) => {
        let arr = []
        for (var i = 0; i < data.length; i++) {
          arr.push({
            value: data[i].value,
            symbolPosition: 'end',
          })
        }
        return arr
      }
      // console.log(getSymbolData(data));
      let option = {
        grid: {
          top: '6%',
          bottom: '6%',
          right: 20,
          left: '7%',
          containLabel: true,
        },
        xAxis: {
          show: false,
        },
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          axisPointer: {
            type: 'shadow',
          },
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
          formatter: (params) => {
            return params[0].name + ' : ' + echarts.format.addCommas(params[0].value) + '条'
          },
        },
        yAxis: [
          {
            triggerEvent: true,
            show: true,
            inverse: true,
            data: getArrByKey(data, 'name'),
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },

            axisLabel: {
              show: true,
              interval: 0,
              color: '#fff',
              align: 'left',
              margin: 60,
              formatter: function (value, index) {
                return '{title|' + value + '}'
              },
              rich: {
                title: {
                  width: 50,
                  align: 'right',
                  fontSize: 36,
                },
              },
            },
          },
          {
            triggerEvent: true,
            show: true,
            inverse: true,
            data: getArrByKey(data, 'name'),
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              shadowOffsetX: '-20px',
              color: ['#fff'],
              align: 'left',
              verticalAlign: 'center',
              lineHeight: 40,
              fontSize: 36,
              formatter: function (value, index) {
                return data[index].value
              },
            },
          },
        ],
        series: [
          {
            name: 'XXX',
            type: 'pictorialBar',
            symbol: 'none',
            symbolSize: 10,
            symbolOffset: [5, 0],
            z: 12,
            itemStyle: {
              normal: {
                color: '#fff',
              },
            },
            data: getSymbolData(data),
            tooltip: {
              show: false,
            },
          },
          {
            name: '政策数',
            type: 'bar',
            showBackground: true,
            yAxisIndex: 0,
            data: data,
            barWidth: 20,
            // align: left,
            itemStyle: {
              normal: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(231, 121, 48, 0)', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'rgba(231, 121, 48, 1)', // 100% 处的颜色
                    },
                  ],
                },
              },
            },
          },
        ],
      }

      myChart.setOption(option, true)
      myChart.on('click', (params) => {
        this.dfIndex3 = 1
        this.page2 = 1
        if (params.componentType == 'series') {
          let qx = params.data.name
          this.cityName = qx
          this.getCashList()
        } else {
          let qx = params.value
          this.cityName = qx
          this.getCashList()
        }
      })
      tools.loopShowTooltip(myChart, option, { loopSeries: true })
    },
    getCashList() {
      let _this = this
      getCsdnInterface1('kfq_qyhx_ssdf', { sqdfsj: this.cityName, pagenum: this.page2, pagesize: 10 }).then((res) => {
        let data = res.data.data[0]
        console.log(222, data)
        _this.total2 = data.total
        _this.cashData = data.result
      })
    },
    initpart3() {
      getCsdnInterface1('qyhx_kfq_dfcs').then((res) => {
        let data = res.data.data.map((item) => {
          return {
            name: item.ywwd1,
            value: item.tjz,
          }
        })
        this.barLineCharts2('chart061', data)
      })
    },
    async initFun() {
      this.getChart1('chart01', this.chartData1)
    },
    getChart1(id, chartData) {
      let myChart = echarts.init(document.getElementById(id))
      let xData = chartData.map((item) => {
        return item.name
      })
      let yData = chartData.map((item) => {
        return item.value
      })
      let option = {
        grid: {
          top: '6%',
          bottom: '-10%',
          left: '5%',
          right: '2%',
          containLabel: true,
        },
        xAxis: {
          show: false,
          max: 100,
        },
        yAxis: [
          {
            inverse: true,
            data: xData,
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
          },
          {
            inverse: true,
            data: yData,
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              shadowOffsetX: '-20px',
              color: '#FD852E',
              align: 'right',
              verticalAlign: 'bottom',
              lineHeight: 50,
              fontSize: 36,
              formatter: function (value, index) {
                return value + '家'
              },
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'bar',
            showBackground: true,
            barBorderRadius: 30,
            yAxisIndex: 0,
            data: chartData.map((item) => {
              return item.value > 100 ? 100 : item.value
            }),
            barWidth: 10,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  1,
                  0,
                  [
                    {
                      offset: 0,
                      color: 'rgba(19,194,194,0.1)',
                    },
                    {
                      offset: 1,
                      color: '#13C2C2',
                    },
                  ],
                  false
                ),
                barBorderRadius: 10,
              },
              barBorderRadius: 4,
            },
            label: {
              normal: {
                color: '#DCEFFF',
                show: true,
                position: [0, '-40px'],
                textStyle: {
                  fontSize: 32,
                },
                formatter: function (a, b) {
                  return a.name
                },
              },
            },
          },
        ],
      }

      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
    getChart2(id, chartData) {
      let myChart = echarts.init(document.getElementById(id))
      let imgUrl2 = require('@/pages/qyzf/img/people.png')
      const option = {
        tooltip: {
          trigger: 'item',
          borderWidth: 0,
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: 'vertical',
          left: '10%',
          top: '70%',
          icon: 'circle',
          itemGap: 20,
          textStyle: {
            color: '#D6E7F9',
            fontSize: 28,
            rich: {
              percent: {
                color: '#ffd79b',
                fontSize: 28,
              },
            },
          },
          formatter: function (name) {
            var data = option.series[0].data //获取series中的data
            var total = 0
            var tarValue
            for (var i = 0, l = data.length; i < l; i++) {
              total += Number(data[i].value)
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            var p = (tarValue / total) * 100
            return name
            // return name + ' ' + '{percent|' + tarValue + '人}' + ' '
          },
        },
        graphic: [
          // {
          //   z: 4,
          //   type: "image",
          //   id: "logo1",
          //   left: "4.5%",
          //   top: "26%",
          //   z: -10,
          //   bounding: "raw",
          //   rotation: 0, //旋转
          //   origin: [50, 50], //中心点
          //   scale: [0.8, 0.8], //缩放
          //   style: {
          //     image: imgUrl1,
          //     opacity: 1,
          //   },
          // },
          {
            z: 5,
            type: 'image',
            id: 'logo2',
            left: '40%',
            top: '32.5%',
            z: -10,
            bounding: 'raw',
            rotation: 0, //旋转
            origin: [50, 50], //中心点
            scale: [1, 1], //缩放
            style: {
              image: imgUrl2,
              opacity: 1,
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['60%', '70%'],
            center: ['50%', '40%'],
            itemStyle: {
              normal: {
                borderColor: '#0A1934',
                // borderWidth: 10
              },
            },
            label: {
              show: false,
            },
            data: chartData,
          },
        ],
      }
      myChart.setOption(option)
      tools.loopShowTooltip(myChart, option, {
        loopSeries: true,
      })
    },
    getChart3(id, chartData) {
      let myChart = echarts.init(document.getElementById(id))
      let xData = [],
        yData = [],
        yData2 = []
      chartData.forEach((item) => {
        xData.push(item.name)
        yData.push(item.value)
        yData2.push(item.value1)
      })
      let option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          axisPointer: {
            type: 'shadow',
          },
          textStyle: {
            color: 'white',
            fontSize: '32',
          },
        },
        grid: {
          left: '8%',
          right: '8%',
          top: '25%',
          bottom: '4%',
          // containLabel: true,
        },
        legend: {
          top: '2%',
          left: 'center',
          textStyle: {
            color: '#CFD7E5',
            fontSize: 28,
          },
        },
        xAxis: [
          {
            type: 'category',
            data: xData,
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              padding: 10,
              textStyle: {
                color: '#fff',
                fontSize: 30,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位：个',
            type: 'value',
            nameTextStyle: {
              fontSize: 28,
              color: '#fff',
              padding: [5, 0, 0, 0],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#fff',
              },
            },
          },
          {
            name: '单位：个',
            type: 'value',
            nameTextStyle: {
              fontSize: 28,
              color: '#fff',
              padding: [5, 0, 0, 0],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 32,
                color: '#fff',
              },
            },
          },
        ],
        series: [
          {
            name: '专利新增',
            type: 'line',
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              normal: {
                color: '#FFC460',
                lineStyle: {
                  color: '#FFC460',
                  width: 1,
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(177,113,46,0.5)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(177,113,46,0)',
                    },
                  ]),
                },
              },
            },
            data: yData,
          },
          {
            name: '专利总数',
            type: 'line',
            yAxisIndex: 1,
            stack: '总量',
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              normal: {
                color: '#0091FF',
                lineStyle: {
                  color: '#0091FF',
                  width: 1,
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(0,145,255,0.5)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(0,145,255,0)',
                    },
                  ]),
                },
              },
            },
            data: yData2,
          },
        ],
      }
      myChart.setOption(option)
      tools.loopShowTooltip(myChart, option, {
        loopSeries: true,
      })
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  computed: {},
  watch: {},
}
</script>

<style scoped lang="less">
.qyzf-right {
  width: 2100px;
}
/* 标题 */
.first-title {
  position: relative;
  width: 100%;
  height: 95px;
  background-image: url('@/pages/qyzf/img/first-title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-left: 100px;
  padding-right: 50px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0;
  line-height: 110px;
  font-size: 45px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
}
.first-title > i {
  position: absolute;
  width: 1062px;
  height: 90px;
  display: inline-block;
  background: url('@/pages/qyzf/img/line.png');
  background-size: 100%;
  animation-name: animateLine;
  animation-duration: 10s;
  animation-iteration-count: infinite;
  /* animation-direction:alternate; */
  /* animation-delay */
}
@keyframes animateLine {
  0% {
    left: -20%;
    opacity: 0.4;
  }
  50% {
    opacity: 1;
  }
  100% {
    left: 70%;
    opacity: 0.2;
  }
}
.second-title {
  width: 100%;
  height: 90px;
  font-size: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dcefff;
  background-image: url('@/pages/qyzf/img/second-title.png');
  background-size: 100% 100%;
  /* background: linear-gradient( 268deg, rgba(0,121,227,0) 0%, rgba(28,148,249,0.5882) 51%, rgba(8,17,26,0) 100%); */
  border-radius: 0px 0px 0px 0px;
  margin-bottom: 20px;
  position: relative;
}
/* 鼠标禁用事件 */
.mouse-no {
  pointer-events: none;
}
.mouse-pointer {
  cursor: pointer;
}
.mouse-not {
  /* cursor: not-allowed; */
  cursor: default;
}
/* 效果 */
.red-color {
  background-image: linear-gradient(#ff6363 10%, #ffffff 60%, #ff7777be 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}
.yel-color {
  background-image: linear-gradient(#ffd79b 10%, #ffffff 60%, #ffc559 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}

.blue-color {
  background-image: linear-gradient(#caffff 10%, #ffffff 60%, #00c0ff 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}
.orange-color {
  background-image: linear-gradient(#ffe2cd 10%, #ffffff 60%, #fd852e 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}
.green-color {
  background-image: linear-gradient(#f0ffd7 10%, #ffffff 60%, #a9db52 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}
/* 表格 */
.table {
  width: 100%;
}

.table-th {
  width: 100%;
  height: 60px;
  background-color: #00396f;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.th {
  flex: 0.3;
  font-size: 32px;
  /* text-align: center; */
  color: #77b3f1;
  margin-left: 10px;
}

.table-tr {
  width: 100%;
  height: 288px;
  overflow-y: auto;
}

.table-tr::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.table-tr::-webkit-scrollbar-thumb {
  border-radius: 4px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}

.tr {
  width: 100%;
  padding: 10px 0;
  background-color: #0f2b4d;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.td {
  flex: 0.3;
  font-size: 32px;
  color: #fff;
  margin-left: 10px;
  /* text-align: center; */
}
.td > span:last-child {
  margin-left: 10px;
}

/* 下拉框 */

.select {
  display: inline-block;
  width: 300px;
  height: 55px;
  text-align: right;
  position: absolute;
  right: 120px;
  top: 0;
  z-index: 100;
}

.flow-icon {
  width: 25px;
  position: absolute;
  top: 20px;
  right: 14px;
}

.flow-icon > img {
  width: 25px;
}

.ul > div {
  width: 100%;
  height: 60px;
  line-height: 60px;
}

.ul {
  width: 100%;
  height: 60px;
  text-align: center;
  font-size: 32px;
  color: #fefefe;
  background-color: #132c4e;
  border: 1px solid #359cf8;
  border-radius: 40px;
  margin-top: 25px;
}
.ul > ul {
  overflow-y: auto;
  display: none;
}
.ul ul > li {
  width: 100%;
  height: 62px;
  line-height: 62px;
  background-color: #132c4ec2;
  padding-right: 20px;
  box-sizing: border-box;
}

.ul ul > li:hover {
  background-color: #359cf8;
}

.ul-active {
  display: block !important;
}

.ul > ul > li:first-of-type {
  border-radius: 40px 40px 0 0;
}

.ul ul::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.ul ul::-webkit-scrollbar-thumb {
  border-radius: 6px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}
.s-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}
* {
  margin: 0;
  padding: 0;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

[v-cloak] {
  display: none;
}

.qyhx-right-container {
  width: 2070px;
  height: 1904px;
  padding: 10px 55px 30px;
  box-sizing: border-box;
  /* background-color: #000; */
  overflow: hidden;
}

.xzscl-con {
  display: flex;
  justify-content: space-between;
}

.qyzfright-con-item {
  width: 33%;
  position: relative;
  margin-right: 42px;
  margin-top: 52px;
  &:last-child {
    margin-right: 0;
  }
}

.qyzfright-pie_bg {
  position: absolute;
  top: 231px;
  left: 62px;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.font-36 {
  font-size: 36px;
}

/* 表格 */
.table {
  width: 960px;
  height: 500px;
  /* padding: 10px; */
  /* box-sizing: border-box; */
}

.table1 {
  width: 650px;
  height: 690px;
}

.table1 .th {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 27px;
  line-height: 60px;
  color: #dcefff;
  margin-left: 0;
  text-align: center;
  font-weight: 700;
}

.table1 .th_td {
  letter-spacing: 0px;
  text-align: center;
}

.table1 .tbody {
  width: 100%;
  height: calc(100% - 60px);
  /* overflow-y: auto; */
  overflow: hidden;
}

.table1 .tbody:hover {
  overflow-y: auto;
}

.table1 .tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.table1 .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table1 .tr {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 78px;
  line-height: 78px;
  font-size: 30px;
  color: #dcefff;
  /* cursor: pointer; */
  box-sizing: border-box;
  background-color: #0f2b4d;
  /* border-bottom: 1px solid #1d3c5f; */
  margin-bottom: 0;
  padding: 0 30px;
  box-sizing: border-box;
}

.table1 .tr_td {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  letter-spacing: 0px;
  text-align: center;
  box-sizing: border-box;
}

.table1 .tr_td > img {
  position: relative;
  top: 25px;
}

.table1 .tr:nth-child(2n) {
  background: #19345833;
  /* background: rgba(10, 39, 76); */
}

.table1 .tr:nth-child(2n + 1) {
  /* background: rgba(15, 49, 95); */
  background: #19345899;
}

.table1 .tr:hover {
  background-color: #3e74aa70;
}

.title-item {
  font-size: 32px;
  color: rgba(255, 255, 255, 0.7);
  display: flex;
}

.title-item > li {
  margin-right: 50px;
}

.title-item > li:last-child {
  margin-right: 0;
}

.title-item-active {
  color: #fff;
}

.title-text {
  line-height: 110px;
  font-size: 50px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
}

.con {
  width: 100%;
  height: 880px;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.con > div {
  width: 49%;
  overflow: hidden;
}

.hqzc-top {
  width: 100%;
  height: 590px;
  display: flex;
  justify-content: space-between;
  align-content: space-between;
}

#cyjj-chart {
  width: 620px;
  height: 760px;
}

.title-img {
  position: absolute;
  top: 32px;
  right: 45px;
  cursor: pointer;
}

.chart-detail {
  width: 324px;
  height: 531px;
  background: url('@/pages/qyzf/img/log_bg.png') no-repeat -82px 0px;
  background-size: 150% 100%;
  position: relative;
  color: #ffffff;
  font-size: 32px;

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.mt_1 {
  margin-top: 20px;
}

.mb_1 {
  margin-bottom: 20px;
}

.detail-top {
  height: 72px;
  line-height: 72px;
  /* margin-top:52px; */
  /* margin-top: 208px; */
  font-weight: bolder;
  padding-left: 50px;
}

.detail-con {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  margin-top: 20px;
  white-space: nowrap;
}

.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
  background: #5f7b96;
  color: #fff;
  font-size: 26px;
  font-weight: normal;
  height: 60px;
  line-height: 58px;
  box-sizing: border-box;
}

.el-pager li.active + li {
  border-left: 1px solid transparent !important;
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
  background: #0166a6;
  border-radius: 3px 3px 3px 3px;
  height: 60px;
  line-height: 60px;
  box-sizing: border-box;
}

.el-pager li {
  background: #5f7b96;
  padding: 0 20px;
  border: 1px solid transparent;
  box-sizing: border-box;
}

.el-pagination .btn-next .el-icon,
.el-pagination .btn-prev .el-icon {
  font-size: 30px;
}

.el-pagination .btn-next,
.el-pagination .btn-prev {
  width: 60px;
}

.el-pagination__total {
  color: #fff;
  font-size: 32px !important;
  margin: 0;
  padding-right: 20px;
  margin-top: 15px;
}
</style>