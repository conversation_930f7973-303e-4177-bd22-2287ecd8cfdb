<template>
  <div class="chart-item">
    <SubTitle title="人员结构" />
    <div class="chart-container">
      <div class="structure-charts">
        <div id="ageChart" class="age-chart"></div>
      </div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'PopulationStructure',
  components: {
    SubTitle,
  },
  mounted() {
    this.fetchPopulationStructureData()
  },
  methods: {
    // 获取人员结构数据
    async fetchPopulationStructureData() {
      try {
        // 使用与FertilityStatus相同的接口调用方式
        const titleType = '人员结构-年龄'
        const response = await getCsdnInterface1('rkhl_test', { type: titleType })

        console.log('人员结构接口返回数据:', response.data)

        // 处理接口返回的数据
        if (response.data && response.data.successful && response.data.data) {
          const processedData = this.processStructureData(response.data.data)
          this.initAgeChart(processedData.ageData)
        } else {

        }
      } catch (error) {
      }
    },

    // 处理人员结构数据
    processStructureData(data) {
      if (Array.isArray(data) && data.length > 0) {
        // 处理年龄数据，直接使用接口返回的数据结构
        const ageData = data.map(item => ({
          name: item.name,
          maleValue: parseFloat(item.value) || 0,    // 男性数据
          femaleValue: parseFloat(item.value1) || 0  // 女性数据
        }))

        // 计算总的男女比例
        const totalMale = ageData.reduce((sum, item) => sum + item.maleValue, 0)
        const totalFemale = ageData.reduce((sum, item) => sum + item.femaleValue, 0)
        const total = totalMale + totalFemale

        const malePercent = total > 0 ? (totalMale / total) * 100 : 50
        const femalePercent = total > 0 ? (totalFemale / total) * 100 : 50

        return { malePercent, femalePercent, ageData }
      }

      // 默认数据
      return {
        malePercent: 52.1,
        femalePercent: 47.9,
        ageData: [
          { name: '0—17 岁', maleValue: 4.59, femaleValue: 4.37 },
          { name: '18 岁—34岁', maleValue: 5.19, femaleValue: 5.02 },
          { name: '35 岁—60 岁', maleValue: 12.51, femaleValue: 12.44 },
          { name: '60 岁以上', maleValue: 7.09, femaleValue: 7.18 },
        ]
      }
    },

    // 性别结构图表
    initGenderChart(malePercent, femalePercent) {
      const chart = this.$echarts.init(document.getElementById('genderChart'))
      const bodyMax = 100 // 指定图形界限的值

      // 导入图片资源
      const menImg = require('@/assets/img/rkhl/men.png')
      const men2Img = require('@/assets/img/rkhl/men2.png')
      const womenImg = require('@/assets/img/rkhl/women.png')
      const women2Img = require('@/assets/img/rkhl/women2.png')

      const labelSetting = {
        normal: {
          show: true,
          position: 'bottom',
          offset: [0, -150],
          formatter: function (param) {
            return ((param.value / bodyMax) * 100).toFixed(1) + '%'
          },
          textStyle: {
            fontSize: 24,
            color: '#fff',
          },
          z: 20,
        },
      }

      const option = {
        tooltip: {
          show: false, // 鼠标放上去显示悬浮数据
        },
        grid: {
          top: '10%',
          left: '20%',
          bottom: '15%',
          containLabel: true,
        },
        xAxis: {
          data: ['男性', '女性'],
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
        },
        yAxis: {
          max: bodyMax,
          splitLine: {
            show: false,
          },
          axisTick: {
            // 刻度线
            show: false,
          },
          axisLine: {
            // 轴线
            show: false,
          },
          axisLabel: {
            // 轴坐标文字
            show: false,
          },
        },
        series: [
          {
            name: '',
            type: 'pictorialBar',
            symbolClip: true,
            symbolBoundingData: bodyMax,
            label: labelSetting,
            data: [
              {
                value: malePercent,
                symbol: 'image://' + men2Img,
                itemStyle: {
                  normal: {
                    color: '#2bbded', // 单独控制颜色
                  },
                },
              },
              {
                value: femalePercent,
                symbol: 'image://' + women2Img,
                itemStyle: {
                  normal: {
                    color: '#eac253', // 单独控制颜色
                  },
                },
              },
            ],
            z: 20,
          },
          {
            // 设置背景底色，不同的情况用这个
            name: 'full',
            type: 'pictorialBar', // 异型柱状图 图片、SVG PathData
            symbolBoundingData: bodyMax,
            animationDuration: 0,
            itemStyle: {
              normal: {
                color: '#ccc', // 设置全部颜色，统一设置
              },
            },
            z: 10,
            data: [
              {
                itemStyle: {
                  normal: {
                    color: 'rgba(68, 195, 234, 0.80)', // 单独控制颜色
                  },
                },
                value: 100,
                symbol: 'image://' + menImg,
              },
              {
                itemStyle: {
                  normal: {
                    color: 'rgba(242, 168, 102, 0.40)', // 单独控制颜色
                  },
                },
                value: 100,
                symbol: 'image://' + womenImg,
              },
            ],
          },
        ],
      }

      chart.setOption(option)
      chart.getZr().on('mousemove', () => {
        chart.getZr().setCursorStyle('default')
      })
    },

    // 年龄结构发散条形图（左男右女）
    initAgeChart(ageData) {
      const chart = this.$echarts.init(document.getElementById('ageChart'))

      // 使用实际的年龄段男女数据
      const ageGroups = ageData.map(item => item.name)
      const maleData = ageData.map(item => -item.maleValue) // 负值显示在左侧
      const femaleData = ageData.map(item => item.femaleValue) // 正值显示在右侧

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          textStyle: {
            color: '#fff',
            fontSize: 24,
          },
          formatter: function(params) {
            let result = params[0].name + '<br/>'
            params.forEach(param => {
              const value = Math.abs(param.value).toFixed(1)
              result += `<span style="color:${param.color}">${param.seriesName}: ${value}%</span><br/>`
            })
            return result
          }
        },
        legend: {
          data: ['男性', '女性'],
          top: '5%',
          textStyle: {
            color: '#fff',
            fontSize: 24,
          },
        },
        grid: {
          left: '10%',
          right: '10%',
          top: '20%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 24,
            formatter: function(value) {
              return Math.abs(value) + '%'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#333'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#333'
            }
          }
        },
        yAxis: {
          type: 'category',
          data: ageGroups,
          axisLabel: {
            color: '#fff',
            fontSize: 24,
          },
          axisLine: {
            lineStyle: {
              color: '#333'
            }
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '男性',
            type: 'bar',
            stack: 'total',
            data: maleData,
            itemStyle: {
              color: '#0AB7FF'
            },
            barWidth: 30,
          },
          {
            name: '女性',
            type: 'bar',
            stack: 'total',
            data: femaleData,
            itemStyle: {
              color: '#EA7E2C'
            },
            barWidth: 30,
          }
        ]
      }
      chart.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .structure-charts {
      width: 100%;
      height: 470px;

      .age-chart {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
