<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-04-14 11:38:22
 * @LastEditors: wjb
 * @LastEditTime: 2025-06-11 15:25:44
-->
<template>
  <div class="qyzf-container">
    <div class="inneLeft">
      <cylLeft class="animate__animated animate__fadeInLeft" />
    </div>
    <div class="midBottom animate__animated animate__fadeIn" style="margin-top: 50px;">
      <cylCenter />
    </div>
    <div class="innerRight">
      <cylRight class="animate__animated animate__fadeInLeft" />
    </div>
  </div>
</template>

<script>
import wrapbox from '@/components/wrapbox'
import cylLeft from './components/dyCylLeft.vue'
import cylCenter from './components/dyCylCenter.vue'
import cylRight from './components/dyCylRight.vue'

export default {
  name: 'index',
  data() {
    return {
      visible: false,
      qyInfoVisible: false,
      qylx: '',
      allMessage: [],
    }
  },
  components: {
    wrapbox,
    cylLeft,
    cylCenter,
    cylRight,
  },
  computed: {},
  mounted() {},
  methods: {},
  watch: {},
}
</script>

<style scoped lang="less">
.qyzf-container {
  position: relative;
  width: 100%;
  height: 100%;
  // z-index: 2;
  // background: url('~@/assets/application/dtBg.png') no-repeat center center;
  // background-size: 100% 100%;
  .inneLeft {
    position: absolute;
    top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .innerRight {
    position: absolute;
    top: 229px;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    width: 2241px;
    height: 1900px;
    z-index: 20;
  }
  .midBottom {
    z-index: 2;
    position: absolute;
    left: 2940px;
    top: 220px;
    width: 2400px;
    height: 1900px;
    z-index: 20;
  }
}
</style>
