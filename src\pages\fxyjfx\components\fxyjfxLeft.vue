<template>
  <div class="fxyjfxLeft">
    <div class="left1">
      <MainTitle title="重点区域预警" />
      <SubTitle title="24小时人流变化情况" />
      <div id="rlbh_hour" class="rlbh_hour"></div>
      <SubTitle title="近30天人流变化情况" />
      <div id="rlbh_day" class="rlbh_day"></div>
      <SubTitle title="重点区域预警" />
      <div id="zdqy_yj" class="zdqy_yj">
        <CommonTable :height="'600px'" :tableData="tableData" style="margin-top: 16px" :row-click="rowClick"></CommonTable>
      </div>
    </div>
    <div class="left2">
      <MainTitle title="网格精细预警" />
      <SubTitle title="24小时人流变化情况" />
      <div id="rlbh_hour1" class="rlbh_hour1"></div>
      <SubTitle title="近30天人流变化情况" />
      <div id="rlbh_day1" class="rlbh_day1"></div>
      <SubTitle title="网格预警" />
      <div id="wg_yj" class="wg_yj">
        <CommonTable :height="'600px'" :tableData="tableData1" style="margin-top: 16px" :row-click="rowClick"></CommonTable>
      </div>
    </div>
  </div>
</template>

<script>
import MainTitle from '@/components/MainTitle.vue'
import SubTitle from '@/components/SubTitle.vue'
import CommonTable from '@/components/CommonTable'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
export default {
  name: 'fxyjfxLeft',
  components: { MainTitle, SubTitle, CommonTable },
  data() {
    return {
      charts: [], // 存储图表实例
      // 重点区域预警表格数据
      tableData: {
        thead: [
          { label: '区域', property: 'area_name', width: 120, align: 'left' },
          { label: '人数', property: 'people_count', width: 60, align: 'left' },
          { label: '超值%', property: 'exceed_percentage', width: 70, align: 'left' },
          { label: '级别', property: 'warning_level', width: 60, align: 'left' },
          { label: '状态', property: 'status', width: 70, align: 'left' },
          { label: '预警时间', property: 'warning_time', width: 120, align: 'left' },
        ],
        tbody: [],
      },
      // 网格预警表格数据
      tableData1: {
        thead: [
          { label: '区域', property: 'area_name', width: 120, align: 'left' },
          { label: '人数', property: 'people_count', width: 60, align: 'left' },
          { label: '超值%', property: 'exceed_percentage', width: 70, align: 'left' },
          { label: '级别', property: 'warning_level', width: 60, align: 'left' },
          { label: '状态', property: 'status', width: 70, align: 'left' },
          { label: '预警时间', property: 'warning_time', width: 120, align: 'left' },
        ],
        tbody: [],
      },
    }
  },

  mounted() {
    this.$nextTick(() => {
      this.initApi()
    })
  },

  beforeDestroy() {
    // 销毁所有图表实例，防止内存泄漏
    this.charts.forEach((chart) => {
      if (chart && !chart.isDisposed()) {
        chart.dispose()
      }
    })
    this.charts = []
  },

  methods: {
    rowClick(obj){
      // 移除弹框显示，只保留控制台输出
      console.log(obj)
      // 如果之前有弹框逻辑，现在已移除
    },
    initApi() {
      this.getData()
    },
    getData() {
      getCsdnInterface1('rkhl_test', { type: '重点区域预警24' }).then((res) => {
        let resdata = res.data.data
        this.initRlbhHourChart(resdata)
      })
      getCsdnInterface1('rkhl_test', { type: '网格精细预警24' }).then((res) => {
        let resdata = res.data.data
        this.initRlbhHour1Chart(resdata)
      })
      getCsdnInterface1('rkhl_test', { type: '重点区域预警30' }).then((res) => {
        let resdata = res.data.data
        this.initRlbhDayChart(resdata)
      })
      getCsdnInterface1('rkhl_test', { type: '网格精细预警30' }).then((res) => {
        let resdata = res.data.data
        this.initRlbhDay1Chart(resdata)
      })
      getCsdnInterface1('rkhl_zdqy_wg_yj', { warningType: '重点区域预警' }).then((res) => {
        let resdata = res.data.data
        this.tableData.tbody = resdata
      })
      getCsdnInterface1('rkhl_zdqy_wg_yj', { warningType: '网格预警' }).then((res) => {
        let resdata = res.data.data
        this.tableData1.tbody = resdata
      })
    },

    // 处理窗口大小变化
    handleResize() {
      this.charts.forEach((chart) => {
        if (chart && !chart.isDisposed()) {
          chart.resize()
        }
      })
    },

    // 重点区域预警 - 24小时人流变化情况
    initRlbhHourChart(resdata) {
      // 销毁已存在的图表实例
      const chartDom = document.getElementById('rlbh_hour')
      if (chartDom) {
        this.$echarts.dispose(chartDom)
      }
      const chart = this.$echarts.init(chartDom)
      this.charts.push(chart)

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 32,
          },
          formatter: function (params) {
            return `${params[0].axisValue}:00<br/>人数: ${params[0].value}人`
          },
        },
        grid: {
          left: '8%',
          right: '5%',
          top: '15%',
          bottom: '15%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: resdata.map((item) => item.name),
          axisLabel: {
            color: '#fff',
            fontSize: 32,
            interval: 3, // 每4个小时显示一次
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 32,
            formatter: '{value}',
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
            },
          },
        },
        series: [
          {
            type: 'line',
            data: resdata.map((item) => item.value),
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#00c0ff',
              width: 2,
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(0, 192, 255, 0.6)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 192, 255, 0.1)',
                  },
                ],
              },
            },
          },
        ],
      }

      chart.setOption(option)
    },

    // 重点区域预警 - 近30天人流变化情况
    initRlbhDayChart(resdata) {
      // 销毁已存在的图表实例
      const chartDom = document.getElementById('rlbh_day')
      if (chartDom) {
        this.$echarts.dispose(chartDom)
      }
      const chart = this.$echarts.init(chartDom)
      this.charts.push(chart)

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 32,
          },
          formatter: function (params) {
            return `${params[0].axisValue}<br/>人数: ${params[0].value}人`
          },
        },
        grid: {
          left: '8%',
          right: '5%',
          top: '15%',
          bottom: '15%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: resdata.map((item) => item.name),
          axisLabel: {
            color: '#fff',
            fontSize: 32,
            interval: 4, // 每5天显示一次
            // rotate: 45
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 32,
            formatter: '{value}',
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
            },
          },
        },
        series: [
          {
            type: 'line',
            data: resdata.map((item) => item.value),
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#00c0ff',
              width: 2,
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(0, 192, 255, 0.6)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 192, 255, 0.1)',
                  },
                ],
              },
            },
          },
        ],
      }

      chart.setOption(option)
    },

    // 网格精细预警 - 24小时人流变化情况
    initRlbhHour1Chart(resdata) {
      // 销毁已存在的图表实例
      const chartDom = document.getElementById('rlbh_hour1')
      if (chartDom) {
        this.$echarts.dispose(chartDom)
      }
      const chart = this.$echarts.init(chartDom)
      this.charts.push(chart)

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#ffa500',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 32,
          },
          formatter: function (params) {
            return `${params[0].axisValue}:00<br/>人数: ${params[0].value}人`
          },
        },
        grid: {
          left: '8%',
          right: '5%',
          top: '15%',
          bottom: '15%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: resdata.map((item) => item.name),
          axisLabel: {
            color: '#fff',
            fontSize: 32,
            interval: 3, // 每4个小时显示一次
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 32,
            formatter: '{value}',
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
            },
          },
        },
        series: [
          {
            type: 'line',
            data: resdata.map((item) => item.value),
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#ffa500',
              width: 2,
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(255, 165, 0, 0.6)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(255, 165, 0, 0.1)',
                  },
                ],
              },
            },
          },
        ],
      }

      chart.setOption(option)
    },

    // 网格精细预警 - 近30天人流变化情况
    initRlbhDay1Chart(resdata) {
      // 销毁已存在的图表实例
      const chartDom = document.getElementById('rlbh_day1')
      if (chartDom) {
        this.$echarts.dispose(chartDom)
      }
      const chart = this.$echarts.init(chartDom)
      this.charts.push(chart)

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#ffa500',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 32,
          },
          formatter: function (params) {
            return `${params[0].axisValue}<br/>人数: ${params[0].value}人`
          },
        },
        grid: {
          left: '8%',
          right: '5%',
          top: '15%',
          bottom: '15%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: resdata.map((item) => item.name),
          axisLabel: {
            color: '#fff',
            fontSize: 32,
            interval: 4, // 每5天显示一次
            // rotate: 45
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 32,
            formatter: '{value}',
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
            },
          },
        },
        series: [
          {
            type: 'line',
            data: resdata.map((item) => item.value),
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#ffa500',
              width: 2,
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(255, 165, 0, 0.6)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(255, 165, 0, 0.1)',
                  },
                ],
              },
            },
          },
        ],
      }

      chart.setOption(option)
    },
  },
}
</script>

<style scoped lang="less">
* {
  margin: 0;
  padding: 0;
}

.fxyjfxLeft {
  width: 2262px;
  display: flex;
  .left1 {
    width: 48%;
    .rlbh_hour {
      width: 100%;
      height: 464px;
    }
    .rlbh_day {
      width: 100%;
      height: 464px;
    }
    .zdqy_yj {
      width: 100%;
      height: 610px;
    }
  }
  .left2 {
    width: 48%;
    margin-left: 79px;
    .rlbh_hour1 {
      width: 100%;
      height: 464px;
    }
    .rlbh_day1 {
      width: 100%;
      height: 464px;
    }
    .wg_yj {
      width: 100%;
      height: 610px;
    }
  }
}
</style>