<!--
 * @Description: 预警详情弹框
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-09-25 16:00:00
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-25 16:00:00
-->
<template>
  <div class="warning-detail-dialog">
    <Commondialog :title="'事件基本信息'" :dialogFlag="visible" @close="closeDialog" :dialog-width="'1800px'">
      <div class="dialog-content">
        <!-- 左侧内容 -->
        <div class="left-content">
          <div class="info-section">
            <h3 class="section-title">基本情况</h3>
            <div class="info-item">
              <span class="label">名称：</span>
              <span class="value">{{ warningData.type }}</span>
            </div>
            <div class="info-item">
              <span class="label">描述：</span>
              <span class="value">
                {{ warningData.description }}
              </span>
            </div>
            <div class="info-item">
              <span class="label">预警时间：</span>
              <span class="value">{{ warningData.time }}</span>
            </div>
            <div class="info-item">
              <span class="label">处理状态：</span>
              <span class="value level-high">{{ warningData.status }}</span>
            </div>
          </div>

          <!-- 图表区域 -->
          <div class="chart-section">
            <h3 class="section-title">指标数值</h3>
            <div id="warning-chart" class="chart-container"></div>
          </div>
        </div>

        <!-- 右侧内容 -->
        <div class="right-content">
          <h3 class="section-title">事件处置情况</h3>
          <div class="suggestion-list">
            <div class="suggestion-item" v-for="(item, index) in suggestions" :key="index">
              <div class="suggestion-header">
                <span class="suggestion-level">{{ item.name }}</span>
                <span class="suggestion-time">{{ item.c_time }}</span>
              </div>
              <div class="suggestion-content">{{ item.valve }}</div>
            </div>
          </div>
        </div>
      </div>
    </Commondialog>
  </div>
</template>

<script>
import Commondialog from '@/components/Commondialog'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'WarningDetailDialog',
  components: {
    Commondialog,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    warningData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      chart: null,
      suggestions: [],
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.getData()
        })
      } else {
        this.destroyChart()
      }
    },
  },
  methods: {
    closeDialog() {
      this.$emit('close')
    },
    getData() {
      getCsdnInterface1('rkhl_yj_xq', { id: this.warningData.id, type: 1 }).then((res) => {
        let resdata = res.data.data
        this.initChart(resdata)
      })
      getCsdnInterface1('rkhl_yj_xq', { id: this.warningData.id, type: 2 }).then((res) => {
        let resdata = res.data.data
        this.suggestions = resdata
      })
    },

    initChart(resdata) {
      const chartDom = document.getElementById('warning-chart')
      if (!chartDom) return

      if (this.chart) {
        this.chart.dispose()
      }

      this.chart = this.$echarts.init(chartDom)

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 24,
          },
        },
        grid: {
          left: '10%',
          right: '10%',
          top: '15%',
          bottom: '15%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: resdata.map(item => item.name),
          axisLabel: {
            color: '#fff',
            fontSize: 20,
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 20,
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
            },
          },
        },
        series: [
          {
            type: 'line',
            data: resdata.map(item => Number(item.valve)),
            smooth: true,
            symbol: 'circle',
            symbolSize: 4,
            lineStyle: {
              color: '#00c0ff',
              width: 2,
            },
            itemStyle: {
              color: '#00c0ff',
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(0, 192, 255, 0.6)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 192, 255, 0.1)',
                  },
                ],
              },
            },
          },
        ],
      }

      this.chart.setOption(option)
    },

    destroyChart() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },
  },

  beforeDestroy() {
    this.destroyChart()
  },
}
</script>

<style lang="less" scoped>
::v-deep.dialog-content {
  display: flex;
  height: 1200px;
  color: #fff;
  padding: 30px;

  .left-content {
    flex: 1;
    padding-right: 20px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);

    .info-section {
      margin-bottom: 30px;

      .info-item {
        display: flex;
        margin-bottom: 30px;
        font-size: 28px;

        .label {
          width: 200px;
          color: #a0e3ff;
          flex-shrink: 0;
        }

        .value {
          color: #fff;
          flex: 1;

          &.level-high {
            color: #ff6b6b;
            font-weight: bold;
          }
        }
      }
    }

    .chart-section {
      .chart-container {
        width: 100%;
        height: 400px;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 8px;
      }
    }
  }

  .right-content {
    flex: 0 0 600px;
    padding-left: 40px;

    .suggestion-list {
      .suggestion-item {
        margin-bottom: 40px;
        padding: 30px;
        background: rgba(0, 50, 100, 0.3);
        border-radius: 8px;
        border-left: 3px solid #00c0ff;

        .suggestion-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 16px;

          .suggestion-level {
            color: #00c0ff;
            font-weight: bold;
            font-size: 24px;
          }

          .suggestion-time {
            color: #999;
            font-size: 24px;
          }
        }

        .suggestion-content {
          color: #fff;
          font-size: 24px;
          line-height: 1.4;
        }
      }
    }
  }

  .section-title {
    color: #00c0ff;
    font-size: 32px;
    margin-bottom: 30px;
    padding-bottom: 16px;
  }
}
</style>
